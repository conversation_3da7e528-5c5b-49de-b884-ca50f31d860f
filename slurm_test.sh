#!/bin/bash
#SBATCH --clusters=brain
#SBATCH --partition=akscore.q,gpu.q,gpu_48g.q
###### ### #SBATCH --nodelist=dive513
#SBATCH --exclude=dive602,dive601,dive507
#SBATCH --qos=normal
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=0
#SBATCH --job-name=test_akscore2_v8_casf_1000
#SBATCH --output=%x.out
#SBATCH --error=%x.err


echo "----------"
echo "HOSTNAME = ${HOSTNAME}"
echo "SLURM_JOB_NAME = ${SLURM_JOB_NAME}"
echo "SLURM_CPUS_PER_TASK = ${SLURM_CPUS_PER_TASK}"
echo "CUDA_VISIBLE_DEVICES = ${CUDA_VISIBLE_DEVICES}"
echo "----------"



################## v8
python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v8_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_1000_tl_1.280_vl_13.046_vs1_0.588_vs5_0.731.pth 



# # scoring docking
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v8_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org_5A.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking_5A.pkl.gz --test_type scoring --node_dim 73 --edge_dim 24 --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.477_vl_10.582_vs1_0.551_vs5_0.740.pth 

# #### test_akscore2_v8_litpcba
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v8_litpcba_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --node_dim 73 --edge_dim 24 --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 8 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.477_vl_10.582_vs1_0.551_vs5_0.740.pth  







################# v7
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v7_bind_rmsd_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --node_dim 73 --edge_dim 24 --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_1000_tl_1.329_vl_7.547_vs1_0.687_vs5_0.831.pth 


# # scoring docking
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v7_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org_5A.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking_5A.pkl.gz --test_type scoring --node_dim 73 --edge_dim 24 --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.121_vl_6.902_vs1_0.682_vs5_0.848.pth 

# #### test_akscore2_v7_litpcba
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v7_litpcba_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --node_dim 73 --edge_dim 24 --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 8 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.121_vl_6.902_vs1_0.682_vs5_0.848.pth 






# # # # ############ non-docking v4
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v4_casf2016_screen_platform.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --node_dim 72 --edge_dim 12 --hidden_dim 256 --num_layers 5 --num_workers 4 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_200_tl0.070_tc0.995_vl0.050_vc0.971.pth  


# # # scoring docking
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v4_casf2016_platform.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org_5A.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking_5A.pkl.gz --test_type docking --node_dim 72 --edge_dim 12 --hidden_dim 256 --num_layers 5 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_300_tl0.054_tc0.997_vl0.035_vc0.972.pth 

##### test_akscore2_v4_litpcba
# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v4_litpcba_platform_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --node_dim 72 --edge_dim 12 --hidden_dim 256 --num_layers 5 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_300_tl0.054_tc0.997_vl0.035_vc0.972.pth  












# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v9_rmsd_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 4 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/18_5A_v9_rmsd_best/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_870_tl_0.166_vl_2.258_vs1_0.698_vs5_0.698.pth 




# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v6_no_atd_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 4 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v6_no_atd_best/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_430_tl_2.059_vl_7.476_vs1_0.711_vs5_0.859.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v5_mean_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --atd_score_dir /Arontier/People/junsuha/akscore_project/decoys_screening/result --exclude_file_path /Arontier/People/junsuha/akscore_project/decoys_screening/out_name.txt --hidden_dim 128 --num_layers 5 --atd_dim 5 --num_workers 4 --batch_size 32 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1122_5A_v5_mean_random/lr0.0001_bs32_nd73_ed24_atd5_hd128_nl5/checkpoint_600_tl0.088_tc0.993_vl0.103_vc0.972.pth 




# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v5_mean_litpcba_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --hidden_dim 128 --num_layers 5 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1122_5A_v5_mean_random/lr0.0001_bs32_nd73_ed24_atd5_hd128_nl5/checkpoint_350_tl0.118_tc0.989_vl0.104_vc0.964.pth 


# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v5_litpcba_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --hidden_dim 128 --num_layers 5 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1122_5A_v5_random/lr0.0001_bs32_nd73_ed24_atd5_hd128_nl5_do0.25/checkpoint_210_tl0.131_tc0.985_vl0.144_vc0.959.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v4_litpcba_screen_pred.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --hidden_dim 128 --num_layers 5 --num_workers 4 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio122_5A_v4_refine/lr0.0001_bs32_nd73_ed24_hd128_nl5_ce8/checkpoint_2600_tl0.091_tc0.992_vl0.082_vc0.943.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_litpcba_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/lit-pcba --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 64 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1111_5A_refine_pignet_loss/lr0.0001_bs32_nd73_ed24_hd256_nl5_ce6_ie0/checkpoint_1400_tl2.551_tc0.200_vl5.859_vc0.088.pth  













# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org_5A.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking_5A.pkl.gz --test_type docking --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 1 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_5A_refine/lr0.0001_bs32_nd73_ed24_hd256_nl5_ce8/checkpoint_1700_tl3.134_tc0.755_vl6.809_vc0.686.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v4_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_8A_screening --hidden_dim 128 --num_layers 5 --num_workers 8 --batch_size 64 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio122_8A_v4_refine/checkpoint_1440_tl0.193_tc0.964_vl0.121_vc0.925.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_8A_screening --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 128 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1111_8A_refine_pignet_loss/lr0.0001_bs32_nd73_ed24_hd256_nl5_ce8_ie0/checkpoint_1500_tl2.339_tc0.477_vl5.934_vc0.174.pth  



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016_screen_rmsd.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_8A_screening --hidden_dim 256 --num_layers 5 --cut_edge_dis 8 --num_workers 8 --batch_size 64 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_8A_refine_rmsd/lr0.0001_bs32_nd73_ed24_hd256_nl5_ce8/checkpoint_1460_tl0.082_tc0.990_vl0.027_vc0.985.pth  




# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v3_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org_5A.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking_5A.pkl.gz --test_type docking --hidden_dim 128 --num_layers 3 --cut_edge_dis 4 --num_workers 1 --batch_size 4 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/docking_ratio19_5A_v3_refine/lr0.0001_bs32_nd73_ed24_hd128_nl3_ce4/checkpoint_1800_tl0.229_tc0.966_vl0.385_vc0.927.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v3_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_screening --hidden_dim 128 --num_layers 3 --cut_edge_dis 8 --num_workers 8 --batch_size 64 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/docking_ratio19_5A_v3_refine/lr0.0001_bs32_nd73_ed24_hd128_nl3_ce4/  












# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_pignet_data_casf2016.py --data_dir /Arontier/People/junsuha/akscore_project/pignet_data/casf2016/data2 --test_type docking --hidden_dim 128 --num_layers 5 --num_workers 1 --batch_size 1 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220614_reg/pignet_data_ratio1111_adam0001_nd35/lr0.0001_bs32_nd35_ed24_hd128_nl5/checkpoint_745_tl5.412_tc0.730_vl13.378_vc0.777.pth --node_dim 35

# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_pignet_data_casf2016.py --data_dir /Arontier/People/junsuha/akscore_project/pignet_data/casf2016/data2 --test_type docking --hidden_dim 256 --num_layers 5 --num_workers 1 --batch_size 1 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220614_reg/pignet_data_ratio1111_adam0001/lr0.0001_bs32_nd73_ed24_hd256_nl5/checkpoint_31_tl7.043_tc0.630_vl13.841_vc0.711.pth 


# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016_screen.py --screen_dir /Arontier/People/junsuha/akscore_project/sim_code/data_screening --hidden_dim 128 --num_layers 3 --num_workers 8 --batch_size 64 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220620_reg/screening_ratio118_8A_refine/lr0.0001_bs32_nd73_ed24_hd128_nl3/checkpoint_195_tl4.417_tc0.654_vl7.047_vc0.905.pth  


# python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org4.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking4.pkl.gz --test_type docking --hidden_dim 256 --num_layers 5 --num_workers 1 --batch_size 8 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220620_reg/docking_ratio14_5A_refine/lr0.0001_bs32_nd73_ed24_hd256_nl5/checkpoint_1885_tl0.103_tc0.993_vl0.833_vc0.869.pth 



# # python -u /Arontier/People/hongyiyu/Project/akscore2/test_akscore2_v2_casf2016.py --coreset_native_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org4.pkl.gz --coreset_docking_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking4.pkl.gz --test_type scoring --hidden_dim 256 --num_layers 5 --num_workers 1 --batch_size 8 --model_path /Arontier/People/hongyiyu/Data/akscore2/model_220620_reg/scoring_5A_refine/lr0.0001_bs32_nd73_ed24_hd256_nl5/checkpoint_177_tl2.623_tc0.805_vl3.332_vc0.763.pth  


#### coreset_datas_docking2.pkl.gz
### result_save_dir
#### test_akscore2_v2_casf2016_no_bind.py
### test_akscore2_v2_casf2016.py
