# test_akscore2_v4_dude_platform_screen_pred.py 脚本说明

## 脚本概述

这个脚本是Akscore2项目的一部分，用于使用训练好的图神经网络模型（GATv2）对DUD-E数据集中的蛋白质-配体复合物进行虚拟筛选预测。它主要执行以下功能：

1. 加载预训练的GATv2模型（即Akscore2-v4版本，Non-Dock模型）
2. 读取指定目标蛋白的配体候选分子数据（以图形式表示）
3. 使用模型预测每个配体与目标蛋白结合的可能性（以sigmoid分数表示）
4. 将预测结果保存到指定目录

## 命令行参数

以下是脚本使用的主要命令行参数：

| 参数 | 默认值 | 类型 | 说明 |
|------|--------|------|------|
| `--screen_dir` | `/Arontier/People/junsuha/akscore_project/DUD_E` | str | DUD-E数据集根目录，包含所有目标蛋白的子目录 |
| `--target_name` | `''` | str | 目标蛋白的名称，对应于screen_dir下的子目录名 |
| `--result_save_dir` | `''` | str | 预测结果保存目录（如不指定，则使用模型路径作为基础目录） |
| `--model_path` | `''` | str | 预训练模型文件的路径 (.pth格式) |
| `--node_dim` | `72` | int | 节点特征维度 |
| `--edge_dim` | `12` | int | 边特征维度 |
| `--hidden_dim` | `256` | int | 隐藏层维度 |
| `--num_layers` | `5` | int | GNN层数 |
| `--batch_size` | `4` | int | 批处理大小 |
| `--num_workers` | `4` | int | 数据加载的工作进程数 |

## 数据目录结构

脚本期望的数据目录结构如下：

```
screen_dir/
├── target_name_1/
│   └── glide_graph_one/
│       ├── ligand_1.pkl.gz
│       ├── ligand_2.pkl.gz
│       └── ...
├── target_name_2/
│   └── glide_graph_one/
│       ├── ligand_1.pkl.gz
│       ├── ligand_2.pkl.gz
│       └── ...
└── ...
```

关键说明：

1. `screen_dir`是包含所有目标蛋白数据的根目录
2. 每个目标蛋白（由`target_name`指定）在根目录下有自己的子目录
3. 在每个目标蛋白目录下，有一个名为`glide_graph_one`的子目录，其中包含配体数据文件
   - 脚本中第311行设置: `target_dir = os.path.join(target_dir, "glide_graph_one")`
4. 每个配体数据文件是一个压缩的pickle文件(`.pkl.gz`)，包含蛋白质-配体复合物的图表示

## 输入文件格式和要求

### 输入文件格式

脚本需要`.pkl.gz`格式的输入文件，这些文件是通过gzip压缩的Python pickle文件。根据代码分析，每个文件应该包含一个蛋白质-配体复合物的图数据列表。

从代码的第174-176行可以看出：
```python
with gzip.open(graph_path, 'rb') as f:
    pdb_id_graphs = pickle.load(f)
```

### 图数据格式

每个图数据对象（PyTorch Geometric的Data对象）应包含以下属性：

1. `x`: 节点特征矩阵，表示蛋白质和配体的原子特征
2. `edge_index`: 边索引，表示原子间的连接关系
3. `edge_attr`: 边特征，包含三种类型边的标记和其他特征：
   - `[1, 0, 0, ...]`: 蛋白质内部边
   - `[0, 1, 0, ...]`: 配体内部边
   - `[0, 0, 1, ...]`: 蛋白质-配体交互边
4. `name`: 配体分子的标识符

从脚本的图修改函数`graph_modification`可以看出，原始图数据会被分解为三个组件：
1. 蛋白质图（protein_graph）
2. 配体图（ligand_graph）
3. 蛋白质-配体交互图（protein_ligand_graph）

## 数据处理和模型预测流程

1. 脚本首先解析命令行参数，并加载预训练的GATv2模型
2. 根据`target_name`参数，确定要处理的目标蛋白目录
3. 为目标蛋白创建一个`akscore2_dataset`对象，该对象会扫描目标目录下的所有`.pkl.gz`文件
4. 使用PyTorch的`DataLoader`批量加载数据
5. 对于每个批次的数据：
   - 加载并解压`.pkl.gz`文件，获取蛋白质-配体复合物图
   - 使用`graph_modification`函数将复合物图分解为蛋白质图、配体图和交互图
   - 将这些图输入到模型中进行预测
   - 通过sigmoid函数将预测结果转换为0-1之间的分数
   - 将预测分数和配体名称添加到结果字典中
6. 最后，将所有预测结果保存到输出文件中

## 输出结果格式

预测结果将保存在以下目录结构中：

```
result_save_dir/
└── glide_sp_rmsd/
    └── target_name.txt
```

其中，`target_name.txt`文件是一个制表符分隔的文本文件，包含以下列：
1. 配体名称（经过简化处理）
2. 索引编号
3. 预测分数（保留4位小数）

文件格式示例：
```
ligand_name1    0    0.7843
ligand_name2    1    0.4512
ligand_name3    2    0.9123
...
```

这些分数可以用于对配体进行排序，分数越高表示模型预测该配体与目标蛋白结合的可能性越大。

## 注意事项

1. 模型期望的节点特征维度为72，边特征维度为12，这是v4版本模型的默认设置
2. 脚本使用了GPU加速（如果可用），否则将使用CPU
3. 输入文件必须是gzip压缩的pickle文件（.pkl.gz），包含图数据列表
4. 目标蛋白目录下必须有名为"glide_graph_one"的子目录