#!/usr/bin/env python
import gzip
import os
import re
import shutil
import subprocess
import sys
import tarfile
import zipfile

import numpy as np
import pandas as pd
import rdkit.Chem as Chem
import rdkit.Chem.rdchem as rdchem
import pickle
from rdkit import RDLogger
from rdkit.Chem import AllChem
from rdkit.Chem.rdchem import BondStereo
from rdkit.Chem.rdchem import BondType
from rdkit.Chem.rdchem import HybridizationType

RDLogger.DisableLog('rdApp.*')

import logging
import math
from io import StringIO

import torch
from meeko import MoleculePreparation, PDBQTMolecule
from openbabel import pybel as pb
from openbabel.openbabel import *
from torch_geometric.data import Data

LOG_FORMAT = '%(levelname)s %(process)d %(processName)s %(filename)s:%(lineno)s %(asctime)s\t%(message)s'
logger = logging.getLogger()

# node_attr = Symbol[20] + De<PERSON>ee[7] + NumHs[5] + ImpVal[6] + ExpVal[6] + ExpHs[2] + ImpHs[5] + Hybri[5] + Arom[1] + IsRing + Radical[3] + FormalCharge[7] + Hydropho + donor + acceptor + acidic + basic = [73]
# edge_attr = PLbond[3] + bondtype[4] + Streo[6] + Ring + conjugate + PLbondtype[5] + bond_distance[4]= [24]

os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
# os.environ["CUDA_VISIBLE_DEVICES"] = "0"


prot_atom_hydrophobicity = {
    'MET': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CE': 0,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HE1': 0,
        'HE2': 0,
        'HE3': 0,
        'HG1': 0,
        'HG2': 0,
        'N': 1,
        'O': 1,
        'SD': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'ASP': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CG': 1,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'N': 1,
        'O': 1,
        'OD1': 1,
        'OD2': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'PRO': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CG': 0,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HG1': 0,
        'HG2': 0,
        'N': 0,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'TYR': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CD2': 0,
        'CE1': 0,
        'CE2': 0,
        'CG': 0,
        'CZ': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HE1': 0,
        'HE2': 0,
        'HH': 1,
        'N': 1,
        'O': 1,
        'OH': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'LEU': {
        '1HD1': 0,
        '1HD2': 0,
        '2HD1': 0,
        '2HD2': 0,
        '3HD1': 0,
        '3HD2': 0,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CD2': 0,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HG': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'VAL': {
        '1HG1': 0,
        '1HG2': 0,
        '2HG1': 0,
        '2HG2': 0,
        '3HG1': 0,
        '3HG2': 0,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CG1': 0,
        'CG2': 0,
        'H': 1,
        'HA': 0,
        'HB': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'GLY': {
        'C': 1,
        'CA': 0,
        'H': 1,
        'HA1': 0,
        'HA2': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'ASN': {
        '1HD2': 1,
        '2HD2': 1,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CG': 1,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
        'N': 1,
        'ND2': 1,
        'O': 1,
        'OD1': 1,
        'OXT': 1,
    },
    'THR': {
        '1HG2': 0,
        '2HG2': 0,
        '3HG2': 0,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CG2': 0,
        'H': 1,
        'HA': 0,
        'HB': 0,
        'HG1': 1,
        'N': 1,
        'O': 1,
        'OG1': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'HIS': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD2': 0,
        'CE1': 1,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD2': 0,
        'HE1': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
        'N': 1,
        'ND1': 1,
        'NE2': 1,
        'O': 1,
        'OXT': 1,
    },
    'SER': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HG': 1,
        'N': 1,
        'O': 1,
        'OG': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'PHE': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CD2': 0,
        'CE1': 0,
        'CE2': 0,
        'CG': 0,
        'CZ': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HE1': 0,
        'HE2': 0,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
        'HZ': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
    },
    'ILE': {
        '1HD1': 0,
        '1HG1': 0,
        '1HG2': 0,
        '2HD1': 0,
        '2HG1': 0,
        '2HG2': 0,
        '3HD1': 0,
        '3HG2': 0,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CG1': 0,
        'CG2': 0,
        'H': 1,
        'HA': 0,
        'HB': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'GLU': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 1,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HG1': 0,
        'HG2': 0,
        'N': 1,
        'O': 1,
        'OE1': 1,
        'OE2': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'LYS': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CE': 0,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HE1': 0,
        'HE2': 0,
        'HG1': 0,
        'HG2': 0,
        'HZ1': 1,
        'HZ2': 1,
        'HZ3': 1,
        'N': 1,
        'NZ': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'ARG': {
        '1HH1': 1,
        '1HH2': 1,
        '2HH1': 1,
        '2HH2': 1,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CG': 0,
        'CZ': 1,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HE': 1,
        'HG1': 0,
        'HG2': 0,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
        'N': 1,
        'NE': 1,
        'NH1': 1,
        'NH2': 1,
        'O': 1,
        'OXT': 1,
    },
    'TRP': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CD2': 0,
        'CE2': 0,
        'CE3': 0,
        'CG': 0,
        'CH2': 0,
        'CZ2': 0,
        'CZ3': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HE1': 1,
        'HE3': 0,
        'HH2': 1,
        'HZ2': 0,
        'HZ3': 0,
        'N': 1,
        'NE1': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'GLN': {
        '1HE2': 1,
        '2HE2': 1,
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 1,
        'CG': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HG1': 0,
        'HG2': 0,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
        'N': 1,
        'NE2': 1,
        'O': 1,
        'OE1': 1,
        'OXT': 1,
    },
    'ALA': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HB3': 0,
        'N': 1,
        'O': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'CYS': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HG': 1,
        'N': 1,
        'O': 1,
        'SG': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'CSD': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'N': 1,
        'O': 1,
        'OD1': 1,
        'OD2': 1,
        'SG': 1,
        'OXT': 1,
    },
    'MLY': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CE': 0,
        'CG': 0,
        'CH1': 0,
        'CH2': 0,
        'H': 1,
        'HZ': 0,
        'N': 1,
        'NZ': 1,
        'O': 1,
        'OXT': 1,
    },
    'PCA': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CG': 0,
        'N': 1,
        'O': 1,
        'OE': 1,
        'OXT': 1,
    },
    'PTR': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD1': 0,
        'CD2': 0,
        'CE1': 0,
        'CE2': 0,
        'CG': 0,
        'CZ': 0,
        'H': 1,
        'N': 1,
        'O': 1,
        'O1P': 1,
        'O2P': 1,
        'O3P': 1,
        'OH': 1,
        'P': 1,
        'OXT': 1,
    },
    'CSO': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD': 0,
        'N': 1,
        'O': 1,
        'OD': 1,
        'SG': 1,
        'OXT': 1,
    },
    'KCX': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CE': 0,
        'CG': 0,
        'CX': 0,
        'H': 1,
        'HZ': 1,
        'N': 1,
        'NZ': 1,
        'O': 1,
        'OQ1': 1,
        'OQ2': 1,
        'OXT': 1,
    },
    'ACE': {'C': 1, 'CA': 0, 'HA1': 0, 'HA2': 0, 'HA3': 0, 'O': 1, 'OXT': 1},
    'MSE': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CE': 0,
        'CG': 0,
        'H': 1,
        'N': 1,
        'O': 1,
        'SE': 1,
        'OXT': 1,
        'HN1': 1,
        'HN2': 1,
        'HN3': 1,
    },
    'TPO': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'CG2': 0,
        'H': 1,
        'N': 1,
        'O': 1,
        'O1P': 1,
        'O2P': 1,
        'O3P': 1,
        'OG1': 1,
        'P': 1,
        'OXT': 1,
    },
    'SEP': {
        'C': 1,
        'CA': 0,
        'CB': 0,
        'H': 1,
        'N': 1,
        'O': 1,
        'O1P': 1,
        'O2P': 1,
        'O3P': 1,
        'OG': 1,
        'P': 1,
        'OXT': 1,
    },
    'LLP': {
        'C': 1,
        "C2'": 0,
        'C2': 0,
        'C3': 1,
        "C4'": 0,
        'C4': 0,
        "C5'": 0,
        'C5': 0,
        'C6': 0,
        'CA': 0,
        'CB': 0,
        'CD': 0,
        'CE': 0,
        'CG': 0,
        'H': 1,
        'H21': 0,
        'H22': 0,
        'H23': 0,
        'H41': 0,
        'H42': 0,
        'H51': 0,
        'H52': 0,
        'H6': 0,
        'HA': 0,
        'HB1': 0,
        'HB2': 0,
        'HD1': 0,
        'HD2': 0,
        'HE1': 0,
        'HE2': 0,
        'HG1': 0,
        'HG2': 0,
        'HZ1': 1,
        'HZ2': 1,
        'N': 1,
        'N1': 0,
        'NZ': 1,
        'O': 1,
        'O3': 1,
        'OP1': 1,
        'OP2': 1,
        'OP3': 1,
        'OP4': 1,
        'P': 1,
        'OXT': 1,
    },
    'SO4': {'O1': 1, 'O2': 1, 'O3': 1, 'O4': 1, 'S': 1, 'OXT': 1},
    'HOH': {'O': 1},
    'NA': {'NA': 1},
    'ZN': {'ZN': 1},
    'MG': {'MG': 1},
    'K': {'K': 1},
    'MN': {'MN': 1},
    'CA': {'CA': 1},
    'CD': {'CD': 1},
    'NI': {'NI': 1},
    'CU': {'CU': 1},
    'CAS': {'CA': 1},
    'HG': {'HG': 1},
    'CO': {'CO': 1},
    'FE': {'FE': 1},
    'FE2': {'FE': 1},
    'CS': {'CS': 1},
    'YCM': {'OXT': 1},
    'LI': {'LI': 1},
    'IAS': {'OXT': 1},
    'SR': {'SR': 1},
    'CMT': {'OXT': 1},
    'DAL': {'OXT': 1},
    'CAF': {'CA': 1},
    'CU1': {'CU': 1},
    'GA': {'GA': 1},
    'RB': {'RB': 1},
    'AU': {'AU': 1},
    'IN': {'IN': 1},
}


def _file_reader(in_fn, mode):
    """
    根据文件类型选择合适的文件读取器
    
    该函数自动检测文件是否为gzip压缩格式，
    并返回相应的文件读取器对象。
    
    参数:
        in_fn (str): 输入文件路径
        mode (str): 文件打开模式（如'rt', 'rb'等）
    
    返回:
        file object: 文件读取器对象，gzip或普通文件
    
    用途:
        为处理压缩和非压缩文件提供统一的接口，
        简化文件读取操作。
    """
    if in_fn.endswith('.gz'):
        return gzip.open(in_fn, mode)
    else:
        return open(in_fn, mode)


def read_pdb_block_from_pdb(pdb_fn):
    """
    从PDB格式文件中读取MODEL块数据
    
    该函数用于处理包含多个MODEL的PDB文件，将每个MODEL作为独立的分子结构返回。
    如果文件中没有MODEL标记，则将整个文件内容作为一个结构返回。
    
    参数:
        pdb_fn (str): PDB文件路径，可以是普通文件或gzip压缩文件
    
    产出:
        generator: 生成器，每次产出一个PDB块的字符串内容
    
    功能说明:
        - 检测文件是否包含MODEL标记
        - 如果无MODEL标记：返回整个文件内容
        - 如果有MODEL标记：逐个返回每个MODEL块的内容
        - 自动跳过REMARK行和END行
    """
    has_model = False
    in_pdb = _file_reader(pdb_fn, 'rt')
    for line in in_pdb:
        if line.startswith('MODEL '):
            has_model = True
            break
    in_pdb.close()

    if not has_model:
        in_pdb = _file_reader(pdb_fn, 'rt')
        pdb_block = in_pdb.read()
        in_pdb.close()
        yield pdb_block

    else:
        in_pdb = _file_reader(pdb_fn, 'rt')
        remark_s = []
        buf = None
        find_model = False
        for line in in_pdb:

            if line.startswith('MODEL '):
                find_model = True
                if buf:
                    pdb_block = ''.join(buf)
                    buf = None
                    yield pdb_block
                buf = []
                # buf.extend(remark_s)
                continue

            if line.startswith('END'):
                continue

            if find_model:
                buf.append(line)
            else:
                remark_s.append(line)

        if buf and len(buf) > 0:
            pdb_block = ''.join(buf)
            buf = None
            yield pdb_block


def one_hot(x, allowable_set):
    """
    将值转换为one-hot编码向量
    
    该函数将单个值转换为二进制的one-hot编码向量，广泛用于机器学习中的特征编码。
    
    参数:
        x: 需要编码的值
        allowable_set (list): 允许的值集合，列表最后一个元素作为默认值
    
    返回:
        list: one-hot编码列表，只有目标值对应位置为True，其余为False
    
    示例:
        one_hot('C', ['C', 'N', 'O', 'other']) -> [True, False, False, False]
        one_hot('X', ['C', 'N', 'O', 'other']) -> [False, False, False, True]
    """
    if x not in allowable_set:
        x = allowable_set[-1]
    return list(map(lambda s: x == s, allowable_set))


def Mol22mol(file=None):
    """
    将MOL2格式文件转换为RDKit分子对象列表
    
    使用OpenBabel库将MOL2格式的分子文件转换为PDB格式，
    然后再转换为RDKit可处理的分子对象。
    
    参数:
        file (str): MOL2文件路径
    
    返回:
        list: RDKit分子对象列表，每个元素都是一个分子结构
    
    功能特点:
        - 保留氢原子信息 (removeHs=False)
        - 不使用距离基础的成键 (proximityBonding=False)
        - 自动分配3D立体化学信息
    """
    mols = []
    obconversion = OBConversion()
    obconversion.SetInAndOutFormats('mol2', 'pdb')
    obmol = OBMol()

    notatend = obconversion.ReadFile(obmol, file)
    while notatend:
        mol = Chem.MolFromPDBBlock(
            obconversion.WriteString(obmol),
            removeHs=False,
            proximityBonding=False,
        )
        Chem.AssignStereochemistryFrom3D(mol)
        mols.append(mol)
        obmol = OBMol()
        notatend = obconversion.Read(obmol)
    return mols


def sdf2mol(sdf_file):
    """
    从SDF文件中读取第一个分子结构
    
    SDF(Structure Data File)是常见的化学分子数据交换格式，
    该函数使用RDKit读取SDF文件中的第一个分子。
    
    参数:
        sdf_file (str): SDF文件路径
    
    返回:
        rdkit.Chem.Mol: RDKit分子对象，包含3D坐标和氢原子信息
    
    注意:
        - 保留氢原子 (removeHs=False)
        - 只返回文件中的第一个分子
        - 适用于单分子SDF文件处理
    """
    for mol in Chem.SDMolSupplier(sdf_file, removeHs=False):
        return mol


def pdb2mol(pdb, autobond=True, sanitize=True):
    """
    将PDB文件或PDB字符串转换为RDKit分子对象
    
    该函数支持从文件路径或直接从PDB字符串中解析分子结构，
    并提供灵活的错误处理机制。
    
    参数:
        pdb (str): PDB文件路径或PDB格式字符串
        autobond (bool): 是否基于原子距离自动生成化学键，默认True
        sanitize (bool): 是否进行分子结构验证和清理，默认True
    
    返回:
        rdkit.Chem.Mol: RDKit分子对象，失败时返回None
    
    功能特点:
        - 保留氢原子信息 (removeHs=False)
        - 支持从文件和字符串两种输入方式
        - 异常处理：文件读取失败时尝试解析为字符串
    """
    try:
        return Chem.MolFromPDBFile(
            pdb, removeHs=False, proximityBonding=autobond, sanitize=sanitize
        )
    except:
        return Chem.MolFromPDBBlock(
            pdb, removeHs=False, proximityBonding=autobond, sanitize=sanitize
        )


def pdb2data(file):
    """
    从PDB文件中提取原子坐标和结构信息
    
    该函数解析PDB文件，提取蛋白质原子的详细信息，包括坐标、
    残基类型、原子类型等，用于后续的分子特征化处理。
    
    参数:
        file (str): PDB文件路径或PDB字符串内容
    
    返回:
        tuple: 包含三个元素的元组
            - pocket_mol: RDKit分子对象
            - pocket_heavy: 重原子信息字典 {index: [原子名, 残基名, [x,y,z], index]}
            - bond: 键连接信息字典
    
    处理特点:
        - 自动过滤水分子 (HOH)
        - 分离处理重原子和氢原子
        - 支持ATOM和HETATM记录类型
        - 提取CONECT记录中的键连接信息
    """
    pocket_mol = pdb2mol(file)
    pocket_heavy = []
    pocket_hydrogen = []
    bond = []
    try:
        for line in open(file).readlines():
            if line[0:4] in ['ATOM'] and line.split()[3] == 'H':
                pocket_hydrogen.append(line)
            elif line[0:4] in ['ATOM'] and line.split()[-1] != 'H':
                pocket_heavy.append(line)
            elif line[0:4] in ['HETA'] and line.split()[3] != 'HOH':
                pocket_heavy.append(line)
            elif line[0:4] in ['CONE']:
                bond.append(line.split())
    except:
        for line in file.split('\n'):
            if line[0:4] in ['ATOM'] and line.split()[3] == 'H':
                pocket_hydrogen.append(line)
            elif line[0:4] in ['ATOM'] and line.split()[-1] != 'H':
                pocket_heavy.append(line)
            elif line[0:4] in ['HETA'] and line.split()[3] != 'HOH':
                pocket_heavy.append(line)
            elif line[0:4] in ['CONE']:
                bond.append(line.split())

    name = file[:-4]

    pocket_heavy = {
        idx: [
            line[12:16].strip(),
            line[17:20].strip(),
            [
                float(line[30:38].strip()),
                float(line[38:46].strip()),
                float(line[46:54].strip()),
            ],
            idx,
        ]
        for idx, line in enumerate(pocket_heavy)
    }
    pocket_hydro = {
        int(line[6:11]): [
            line[12:16].strip(),
            line[17:20].strip(),
            [
                float(line[30:38].strip()),
                float(line[38:46].strip()),
                float(line[46:54].strip()),
            ],
        ]
        or not lig_mol
        for idx, line in enumerate(pocket_hydrogen)
    }
    bond = {int(line[1]): [int(num) for num in line[2:]] for line in bond}

    return pocket_mol, pocket_heavy, bond


def get_default_config():
    """
    获取Meeko分子预处理的默认配置参数
    
    该函数返回用于Meeko分子预处理的标准配置，主要用于
    将RDKit分子转换为AutoDock所需的PDBQT格式。
    
    返回:
        dict: 配置参数字典，包含以下键值对：
            - keep_nonpolar_hydrogens: 是否保留非极性氢原子 (False)
            - hydrate: 是否添加水合作用 (False)
            - flexible_amides: 是否允许酰胺键旋转 (True)
            - rigid_macrocycles: 是否强制大环结构为刚性 (False)
            - min_ring_size: 最小环大小 (7)
            - max_ring_size: 最大环大小 (33)
            - double_bond_penalty: 双键旋转惩罚因子 (50)
    """
    config = {
        'keep_nonpolar_hydrogens': False,
        'hydrate': False,
        'flexible_amides': True,
        'rigid_macrocycles': False,
        'min_ring_size': 7,
        'max_ring_size': 33,
        'rigidify_bonds_smarts': [],
        'rigidify_bonds_indices': [],
        'double_bond_penalty': 50,
        'atom_type_smarts': {},
        'add_index_map': False,
        'remove_smiles': False,
    }
    return config


def rdkit_mol_TO_pdbqt(mol, config=None):
    """
    将RDKit分子对象转换为AutoDock PDBQT格式字符串
    
    使用Meeko库将RDKit分子对象预处理并转换为AutoDock对接
    所需的PDBQT格式，包括旋转键分析和原子类型分配。
    
    参数:
        mol (rdkit.Chem.Mol): 输入的RDKit分子对象
        config (dict): Meeko预处理配置参数，为None时使用默认配置
    
    返回:
        str: PDBQT格式字符串，失败时返回None
    
    功能特点:
        - 自动识别和标记旋转键
        - 分配适合对接的原子类型
        - 处理分子的柔性和刚性部分
        - 包含异常处理，防止转换失败
    """
    if not config:
        config = get_default_config()

    mol_counter = 0
    num_skipped = 0

    is_valid = mol is not None
    mol_counter += int(is_valid == True)
    num_skipped += int(is_valid == False)
    if not is_valid:
        return

    preparator = MoleculePreparation.from_config(config)
    preparator.prepare(mol)

    try:
        ligand_prepared = preparator.write_pdbqt_string()
    except RuntimeError:
        return

    return ligand_prepared


def pdbqt2mol(pdbqt):
    """
    将PDBQT文件或字符串转换为RDKit分子对象
    
    该函数支持多种输入格式，包括PDBQT文件、DLG文件和PDBQT字符串，
    使用Meeko和OpenBabel库实现格式转换，并提供异常处理机制。
    
    参数:
        pdbqt (str): PDBQT文件路径、DLG文件路径或PDBQT字符串
    
    产出:
        generator: 生成器，每次产出一个RDKit分子对象
    
    功能特点:
        - 支持多种格式输入：.pdbqt、.dlg、字符串
        - 优先使用Meeko进行转换
        - 失败时使用OpenBabel作为备用方案
        - 自动处理文件中的多个分子姿态
    """
    if pdbqt.endswith('.pdbqt'):
        pose_mol_s = PDBQTMolecule.from_file(pdbqt)
    elif pdbqt.endswith('.dlg'):
        pose_mol_s = PDBQTMolecule.from_file(pdbqt, is_dlg=True)
    else:
        pose_mol_s = PDBQTMolecule(pdbqt.strip())

    for pose_mol in pose_mol_s:
        mol = None
        try:
            mol = pose_mol.export_rdkit_mol()
        except Exception:
            pdbqt_string = pose_mol.write_pdbqt_string()
            obmol = pb.readstring('pdbqt', pdbqt_string)
            obmol.write('pdb')
            str_io = StringIO(sdf_string)
            str_data = str_io.read().encode('utf8')
            mol = Chem.MolFromPDBBlock(str_data, removeHs=True, proximityBonding=False)
        yield mol


def pdbqt2pdbfile(pdbqt_string, filename):
    """
    将PDBQT字符串转换并保存为PDB文件
    
    该函数将PDBQT格式字符串解析为RDKit分子对象，
    提取其中的分子名称和SMILES信息，然后保存为PDB文件。
    
    参数:
        pdbqt_string (str): PDBQT格式字符串内容
        filename (str): 输出PDB文件路径
    
    返回:
        str: 输出PDB文件的路径
    
    功能特点:
        - 自动提取REMARK中的分子名称
        - 使用SMILES信息修正分子的键级数
        - 将分子属性信息保存到PDB文件中
        - 提供错误处理机制
    """
    # logger.info(pdbqt_string)
    for mol in pdbqt2mol(pdbqt_string):
        break
    if not mol:
        return filename
    smiles = None
    for line in pdbqt_string.split('\n'):
        if line.startswith('REMARK  Name'):
            name = line.split('=')[1].strip()
            mol.SetProp('_Name', name)
        if line.startswith('REMARK SMILES') and not line.startswith(
            'REMARK SMILES IDX'
        ):
            smiles = line.split()[2]
    if smiles:
        ref_mol = Chem.MolFromSmiles(smiles)
        mol = AllChem.AssignBondOrdersFromTemplate(ref_mol, mol)
    pdb_string = Chem.MolToPDBBlock(mol)
    fw = open(filename, 'w')
    fw.write(pdb_string)
    fw.close()

    return filename


def poss2data(poss_mols, clus_list=None):
    """
    将Meeko PDBQT分子对象转换为用于图构建的数据结构
    
    该函数将Meeko解析的PDBQT分子对象转换为RDKit分子对象，
    并提取原子的电荷、坐标和类型信息，用于后续的图特征化。
    
    参数:
        poss_mols: Meeko PDBQT分子对象列表或生成器
        clus_list: 可选的聚类列表，用于选择特定姿态
    
    返回:
        tuple: 包含两个元素
            - mol: RDKit分子对象
            - data: 原子数据字典 {atom_idx: [原子符号, 电荷, [x,y,z], 类型, 编号]}
    
    功能特点:
        - 从第一个可用分子中提取数据
        - 结合PDBQT和RDKit信息，获取完整的原子属性
        - 包含异常处理，支持多种解析方式
    """
    for i_mol, mol in enumerate(poss_mols):
        try:
            pdbqt_string = mol.write_pdbqt_string()
            mol = mol.export_rdkit_mol(exclude_hydrogens=False)  # 수소 위치가 필요 없으면 True
        except RuntimeError:
            pdbqt_string = mol.write_pdbqt_string()
            obmol = pb.readstring('pdbqt', pdbqt_string)
            sdf_string = obmol.write('pdb')

            str_io = StringIO(sdf_string)
            str_data = str_io.read().encode('utf8')
            mol = Chem.MolFromPDBBlock(str_data, removeHs=True, proximityBonding=False)
            # bytes_io = BytesIO(str_data)
            # suppl = ForwardSDMolSupplier(bytes_io, removeHs=False)
            # for mol in suppl:
            #    break

        Chem.AssignStereochemistryFrom3D(mol)
        #        mol = Chem.RemoveHs(mol)
        data = {}
        xyz_pdbqt = {}
        # from pdbqt
        for line in pdbqt_string.split('\n'):
            if line.startswith('ATOM') or line.startswith('HETATM'):
                xyz_pdbqt[
                    float(line[30:38].strip()),
                    float(line[38:46].strip()),
                    float(line[46:54].strip()),
                ] = (float(line[71:77].strip()), line[77:79].strip())

        # from mol
        conf = mol.GetConformer()
        nums = 0
        for i, atom in enumerate(mol.GetAtoms()):
            atom_xyz = list(conf.GetAtomPosition(i))
            if (atom_xyz[0], atom_xyz[1], atom_xyz[2]) in xyz_pdbqt:
                data[i] = [
                    atom.GetSymbol(),
                    xyz_pdbqt[atom_xyz[0], atom_xyz[1], atom_xyz[2]][0],
                    atom_xyz,
                    xyz_pdbqt[atom_xyz[0], atom_xyz[1], atom_xyz[2]][1],
                    nums,
                ]
                nums += 1
        break

    return mol, data


def calculateDistance(arr1, arr2):
    """
    计算两组原子坐标间的欧几里得距离矩阵
    
    使用NumPy的广播机制高效计算两组原子间的所有两两组合距离，
    返回一个二维距离矩阵，广泛用于蛋白质-配体相互作用分析。
    
    参数:
        arr1 (numpy.ndarray): 第一组原子的三维坐标，形状为 (N, 3)
        arr2 (numpy.ndarray): 第二组原子的三维坐标，形状为 (M, 3)
    
    返回:
        numpy.ndarray: 距离矩阵，形状为 (N, M)，其中元素 [i,j] 表示 arr1[i] 与 arr2[j] 的距离
    
    计算公式:
        distance[i,j] = ||arr1[i] - arr2[j]||_2
    """
    return np.linalg.norm(arr1[:, None, :] - arr2[None, :, :], axis=-1)


class Featurize:
    def __init__(self):
        """
        初始化分子特征化器类
        
        该类负责将分子结构转换为图神经网络所需的特征向量，
        初始化时定义各种化学官能团的SMARTS模式，用于识别原子的化学属性。
        
        初始化属性:
            H_donor: 氢键供体SMARTS模式，识别能够提供质子的原子
            H_acceptor: 氢键受体SMARTS模式，识别能够接受质子的原子
            acidic: 酸性官能团SMARTS模式，识别羰酸等酸性结构
            basic: 碱性官能团SMARTS模式，识别胺等碱性结构
        
        这些模式用于在原子级别上识别关键的化学属性，
        为构建蛋白质-配体相互作用图提供必要的化学信息。
        """
        self.H_donor = Chem.MolFromSmarts(
            '[$([N;!H0;v3,v4&+1]),$([O,S;H1;+0]),n&H1&+0]'
        )
        self.H_acceptor = Chem.MolFromSmarts(
            '[$([O,S;H1;v2;!$(*-*=[O,N,P,S])]),$([O,S;H0;v2]),$([O,S;-]),$([N;v3;!$(N-*=[O,N,P,S])]),n&H0&+0,$([o,s;+0;!$([o,s]:n);!$([o,s]:c:n)])]'
        )
        self.acidic = Chem.MolFromSmarts('[$([C,S](=[O,S,P])-[O;H1,-1])]')
        self.basic = Chem.MolFromSmarts(
            '[#7;+,$([N;H2&+0][$([C,a]);!$([C,a](=O))]),$([N;H1&+0]([$([C,a]);!$([C,a](=O))])[$([C,a]);!$([C,a](=O))]),$([N;H0&+0]([C;!$(C(=O))])([C;!$(C(=O))])[C;!$(C(=O))])]'
        )

    def AtomNode(self, mol, data, pocket=True):
        """
        生成原子节点的特征向量
        
        该函数为图中的每个原子节点生成73维的特征向量，
        包含原子的物理化学属性和空间信息。
        
        参数:
            mol (rdkit.Chem.Mol): RDKit分子对象
            data (dict): 原子数据字典，包含原子的坐标和电荷信息
            pocket (bool): 是否为蛋白质口袋原子，默认True
        
        返回:
            tuple: 包含两个元素
                - node: 节点特征列表，每个元素是73维numpy数组
                - xyz: 原子坐标列表，每个元素是[x,y,z]坐标
        
        73维特征组成:
            - 原子符号 (20维): C, H, O, N, S, P, F, Cl, Br, I, B, Se, Fe, Ru, Mn, Co, Ni, Cu, Zn, other
            - 原子度数 (7维): 0-6
            - 氢原子数量 (5维): 0-4
            - 隐式价 (6维): 0-5
            - 显式价 (6维): 0-5
            - 显式氢数 (2维): 0-1
            - 隐式氢数 (5维): 0-4
            - 杂化类型 (5维): SP, SP2, SP3, SP3D, SP3D2
            - 形式电荷 (7维): -2到+4
            - 自由基电子数 (3维): 0-2
            - 芳香性、环状态、疏水性 (3维)
            - 氢键供体、受体、酸性、碱性 (4维)
        """
        # AllChem.ComputeGasteigerCharges(mol)
        H_donor_match = sum(mol.GetSubstructMatches(self.H_donor), ())
        H_acceptor_match = sum(mol.GetSubstructMatches(self.H_acceptor), ())
        acidic_match = sum(mol.GetSubstructMatches(self.acidic), ())
        basic_match = sum(mol.GetSubstructMatches(self.basic), ())

        node = []
        xyz = []
        for idx, atom in enumerate(mol.GetAtoms()):
            if idx in data:
                if pocket:
                    try:
                        hydrophobicity = prot_atom_hydrophobicity[data[idx][1]][
                            data[idx][0]
                        ]
                    except:
                        hydrophobicity = 0
                else:
                    hydrophobicity = 0 if abs(data[idx][1]) < 0.2 else 1

                tmp = np.array(
                    one_hot(
                        atom.GetSymbol(),
                        [
                            'C',
                            'H',
                            'O',
                            'N',
                            'S',
                            'P',
                            'F',
                            'Cl',
                            'Br',
                            'I',
                            'B',
                            'Se',
                            'Fe',
                            'Ru',
                            'Mn',
                            'Co',
                            'Ni',
                            'Cu',
                            'Zn',
                            'other',
                        ],
                    )
                    + one_hot(atom.GetDegree(), [0, 1, 2, 3, 4, 5, 6])
                    + one_hot(atom.GetTotalNumHs(), [0, 1, 2, 3, 4])
                    + one_hot(atom.GetImplicitValence(), [0, 1, 2, 3, 4, 5])
                    + one_hot(atom.GetExplicitValence(), [0, 1, 2, 3, 4, 5])
                    + one_hot(atom.GetNumExplicitHs(), [0, 1])
                    + one_hot(atom.GetNumImplicitHs(), [0, 1, 2, 3, 4])
                    + one_hot(
                        atom.GetHybridization(),
                        [
                            HybridizationType.SP,
                            HybridizationType.SP2,
                            HybridizationType.SP3,
                            HybridizationType.SP3D,
                            HybridizationType.SP3D2,
                        ],
                    )
                    + one_hot(atom.GetFormalCharge(), [-2, -1, 0, 1, 2, 3, 4])
                    + one_hot(atom.GetNumRadicalElectrons(), [0, 1, 2])
                    + [atom.GetIsAromatic(), atom.IsInRing(), hydrophobicity]
                    + [
                        idx in H_donor_match,
                        idx in H_acceptor_match,
                        idx in acidic_match,
                        idx in basic_match,
                    ]
                )
                node.append(tmp)
                xyz.append(data[idx][2])

        return node, xyz

    def CommonEdge(self, pocket=False, ligand=False):
        """
        生成边的公共特征编码，用于区分不同类型的化学键
        
        该函数根据输入参数生成三维的one-hot编码，用于标识
        边所连接的原子类型，为后续的图神经网络处理提供重要的结构信息。
        
        参数:
            pocket (bool): 是否为蛋白质口袋原子，默认False
            ligand (bool): 是否为配体原子，默认False
        
        返回:
            list: 三维列表，表示边的类型编码
                - [1, 0, 0]: 蛋白质内部共价键
                - [0, 1, 0]: 配体内部共价键
                - [0, 0, 1]: 蛋白质-配体间非共价相互作用
        
        用途:
            在图神经网络中区分不同类型的分子间相互作用，
            帮助模型学习不同类型连接的特征和重要性。
        """
        if pocket == True and ligand == False:  ## pocket covalent bond
            return [1, 0, 0]
        elif pocket == False and ligand == True:  ## ligand covalent bond
            return [0, 1, 0]
        elif pocket == True and ligand == True:  ## PL non-covalent bond
            return [0, 0, 1]

    def CovalentEdge(self, mol, data, pocket=False, ligand=False, num=0):
        """
        生成分子内部共价键的边特征
        
        该函数提取分子中的共价键信息，包括键类型、立体化学、
        环状态等特征，为图网络中的边生成24维特征向量。
        
        参数:
            mol (rdkit.Chem.Mol): RDKit分子对象
            data (dict): 原子数据字典，包含原子索引映射
            pocket (bool): 是否为蛋白质口袋，默认False
            ligand (bool): 是否为配体，默认False
            num (int): 节点索引偏移量，用于多分子图的节点编号，默认0
        
        返回:
            tuple: 包含两个元素
                - edge_index: 边索引列表，每个元素是[source, target]
                - edge_attr: 边特征列表，每个元素是24维numpy数组
        
        24维边特征组成:
            - 边类型标识 (3维): 蛋白质内/配体内/蛋白质-配体间
            - 键类型 (4维): 单键/双键/三键/芳香键
            - 立体化学 (6维): 不同立体构型
            - 环状态 (1维): 是否在环中
            - 共轭状态 (1维): 是否共轭
            - 特殊键类型 (5维): 疏水键/氢键/离子键等
            - 距离分档 (4维): <2Å, <4Å, <6Å, <8Å
        """
        commonEdge = self.CommonEdge(pocket=pocket, ligand=ligand)
        edge_index, edge_attr = [], []
        for idx, bond in enumerate(mol.GetBonds()):
            if bond.GetBeginAtomIdx() in data and bond.GetEndAtomIdx() in data:
                edge_index.append(
                    [
                        data[bond.GetBeginAtomIdx()][-1] + num,
                        data[bond.GetEndAtomIdx()][-1] + num,
                    ]
                )
                edge_index.append(
                    [
                        data[bond.GetEndAtomIdx()][-1] + num,
                        data[bond.GetBeginAtomIdx()][-1] + num,
                    ]
                )
                tmp = np.array(
                    commonEdge
                    + one_hot(
                        bond.GetBondType(),
                        [
                            BondType.SINGLE,
                            BondType.DOUBLE,
                            BondType.TRIPLE,
                            rdchem.BondType.AROMATIC,
                        ],
                    )
                    + one_hot(
                        bond.GetStereo(),
                        [
                            BondStereo.STEREOANY,
                            BondStereo.STEREOCIS,
                            BondStereo.STEREOE,
                            BondStereo.STEREONONE,
                            BondStereo.STEREOTRANS,
                            BondStereo.STEREOZ,
                        ],
                    )
                    + [bond.IsInRing(), bond.GetIsConjugated()]
                    + [0, 0, 0, 0, 0]
                    + [1, 1, 1, 1]
                )
                edge_attr.append(tmp)
                edge_attr.append(tmp)

        return edge_index, edge_attr

    def PL_NonCovalentEdge(self, pocket_node, ligand_node, distance_matrix):
        """
        生成蛋白质-配体间非共价相互作用边
        
        该函数基于蛋白质原子和配体原子之间的距离和化学性质，
        识别和编码各种非共价相互作用，包括氢键、疏水作用、离子作用等。
        
        参数:
            pocket_node (list): 蛋白质原子节点特征列表
            ligand_node (list): 配体原子节点特征列表
            distance_matrix (numpy.ndarray): 蛋白质-配体原子间距离矩阵
        
        返回:
            tuple: 包含两个元素
                - edge_index: 边索引列表，每个元素是[source, target]
                - edge_attr: 边特征列表，每个元素是24维numpy数组
        
        相互作用类型识别:
            - 疏水作用: 两个原子都为疏水性原子时建立
            - 氢键: 氢键供体和受体之间的相互作用
            - 离子作用: 带电原子间的静电相互作用
            - 距离信息: 根据原子距离分为四个级别
        
        距离阈值:
            只考虑距离小于8Å的原子对，确保相互作用的生物学意义。
        """
        num = len(pocket_node)
        distance_4 = np.array(np.where(distance_matrix <= 8))
        commonEdge = self.CommonEdge(pocket=True, ligand=True)

        edge_index, edge_attr = [], []
        for idx, prot_idx in enumerate(distance_4[0]):

            lig_idx = distance_4[1][idx]
            lig_idx_total = lig_idx + num

            edge_index.append([prot_idx, lig_idx_total])
            edge_index.append([lig_idx_total, prot_idx])

            Hydrophobic = (
                pocket_node[prot_idx][-5],
                ligand_node[lig_idx][-5],
            )  ## if (0,0) == hydrophobic bond
            Hydrophobic_bond = 1 if Hydrophobic == (0, 0) else 0

            PL_Hydrogen = pocket_node[prot_idx][-4], ligand_node[lig_idx][-3]
            PL_Hydrogen_bond = 1 if PL_Hydrogen == (1, 1) else 0

            LP_Hydrogen = pocket_node[prot_idx][-3], ligand_node[lig_idx][-4]
            LP_Hydrogen_bond = 1 if LP_Hydrogen == (1, 1) else 0

            PL_Ionic = pocket_node[prot_idx][-1], ligand_node[lig_idx][-2]
            Ionic_bond_PL = 1 if PL_Ionic == (1, 1) else 0

            LP_Ionic = pocket_node[prot_idx][-2], ligand_node[lig_idx][-1]
            Ionic_bond_LP = 1 if LP_Ionic == (1, 1) else 0

            if distance_matrix[prot_idx][lig_idx] < 2:
                ang_2, ang_4, ang_6, ang_8 = 1, 1, 1, 1
            elif distance_matrix[prot_idx][lig_idx] < 4:
                ang_2, ang_4, ang_6, ang_8 = 0, 1, 1, 1
            elif distance_matrix[prot_idx][lig_idx] < 6:
                ang_2, ang_4, ang_6, ang_8 = 0, 0, 1, 1
            elif distance_matrix[prot_idx][lig_idx] < 8:
                ang_2, ang_4, ang_6, ang_8 = 0, 0, 0, 1

            edge_tmp = (
                commonEdge
                + [0] * 12
                + [
                    Hydrophobic_bond,
                    PL_Hydrogen_bond,
                    LP_Hydrogen_bond,
                    Ionic_bond_PL,
                    Ionic_bond_LP,
                ]
                + [ang_2, ang_4, ang_6, ang_8]
            )
            edge_attr.append(edge_tmp)
            edge_attr.append(edge_tmp)

        return edge_index, edge_attr


def mol_2_graph(ligand_path, protein_file, distance=5, clus_list=None):
    """
    将蛋白质-配体复合物转换为PyTorch Geometric图数据结构
    
    该函数是整个图构建流程的核心，将蛋白质和配体的三维结构
    转换为图神经网络可处理的图数据格式，包含节点特征和边特征。
    
    参数:
        ligand_path (str): 配体文件路径，支持PDB/PDBQT/SDF等格式
        protein_file (str): 蛋白质PDB文件路径
        distance (float): 结合口袋生成的距离阈值（Å），默认5Å
        clus_list: 可选的聚类列表，用于选择特定配体姿态
    
    返回:
        tuple: 包含四个元素
            - node_attrs: 节点特征张量 (N × 73)
            - edge_attrs: 边特征张量 (M × 24)
            - edge_indexs: 边索引张量 (2 × M)
            - error: 错误标志 (1=成功, 0=失败)
    
    处理流程:
        1. 解析配体文件，转换为PDBQT格式
        2. 提取配体的原子坐标和特征
        3. 根据配体位置生成蛋白质结合口袋
        4. 提取蛋白质原子的特征和坐标
        5. 计算蛋白质-配体间距离矩阵
        6. 构建三种类型的边：蛋白质内、配体内、蛋白质-配体间
        7. 将所有特征转换为PyTorch张量
    
    异常处理:
        当任何步骤失败时，返回零填充的默认张量和error=0。
    """
    featurize = Featurize()

    ## pocket file and ligand file to block data
    ## make rdkit.mol for node and edge
    try:
        if ligand_path.endswith('.pdb'):
            ligand_pdbqt = ligand_path + 'qt'
            # 如果PDBQT文件不存在，从PDB文件生成
            if not os.path.exists(ligand_pdbqt):
                mol = pdb2mol(ligand_path)
                ligand_pdbqt_string = rdkit_mol_TO_pdbqt(mol)
                if ligand_pdbqt_string:
                    with open(ligand_pdbqt, 'w') as f:
                        f.write(ligand_pdbqt_string)
                else:
                    raise Exception('无法生成PDBQT文件')
            poss_mols = PDBQTMolecule.from_file(ligand_pdbqt)
        elif ligand_path.endswith('.pdbqt'):
            poss_mols = PDBQTMolecule.from_file(ligand_path)
        elif ligand_path.endswith('.sdf'):
            mol = sdf2mol(ligand_path)
            ligand_pdbqt = rdkit_mol_TO_pdbqt(mol)
            poss_mols = PDBQTMolecule(ligand_pdbqt)
        else:
            poss_mols = PDBQTMolecule(ligand_path, skip_typing=True)

        ligand_mol, ligand_data = poss2data(poss_mols)

        ## node part
        if ligand_mol == None:
            raise Exception('no have ligand')
        pocket_file = make_pocket(protein_file, ligand_data, distance)
        if not pocket_file:
            raise Exception('pocket not have')
        pocket_mol, pocket_data, bond = pdb2data(pocket_file)

        if pocket_mol == None:
            raise Exception('no have pocket')
        node_pocket, xyz_pocket = featurize.AtomNode(
            pocket_mol, pocket_data, pocket=True
        )
        node_ligand, xyz_ligand = featurize.AtomNode(
            ligand_mol, ligand_data, pocket=False
        )
        node_attr = node_pocket + node_ligand
        P_num = len(node_pocket)

        ## calculate distance for Pocket-Ligand non-covalent bond
        distance_matrix_PL = calculateDistance(
            np.array(xyz_pocket), np.array(xyz_ligand)
        )

        ## covalent edge ( Pocket-Pocket, Ligand-Ligand atoms)
        edge_index_pocket, edge_attr_pocket = featurize.CovalentEdge(
            pocket_mol, pocket_data, pocket=True, ligand=False, num=0
        )  # num == match index
        edge_index_ligand, edge_attr_ligand = featurize.CovalentEdge(
            ligand_mol, ligand_data, pocket=False, ligand=True, num=P_num
        )

        ## non covalent edge ( Pocket - Ligand )
        edge_index_PL_non, edge_attr_PL_non = featurize.PL_NonCovalentEdge(
            node_pocket, node_ligand, distance_matrix_PL
        )

        ## total edge for Graph
        edge_index = edge_index_pocket + edge_index_ligand + edge_index_PL_non
        edge_attr = edge_attr_pocket + edge_attr_ligand + edge_attr_PL_non

        node_attrs = torch.tensor(np.array(node_attr), dtype=torch.float)
        edge_attrs = torch.tensor(np.array(edge_attr), dtype=torch.float)
        edge_index = torch.tensor(np.array(edge_index), dtype=torch.long)
        edge_indexs = edge_index.t().contiguous()
        error = 1
    except Exception as E:
        logging.info(f'{E}, error ligand')
        node_attrs = torch.tensor(np.zeros([100, 73]), dtype=torch.float)
        edge_attrs = torch.tensor(np.zeros([1000, 24]), dtype=torch.float)
        edge_index = torch.tensor(np.zeros([1000, 2]), dtype=torch.long)
        edge_indexs = edge_index.t().contiguous()
        error = 0

    ## data to tensor for Graph

    return node_attrs, edge_attrs, edge_indexs, error


def cluster_list(dlg): # is not used, can be safely skipped
    """
    从AutoDock DLG文件中提取聚类信息和结合能量
    
    解析AutoDock对接结果文件(DLG)，提取与天然结合姿态
    RMSD为0.00的所有聚类结果，并获取每个聚类的结合能量。
    
    参数:
        dlg (str): AutoDock DLG结果文件路径
    
    返回:
        tuple: 包含两个元素
            - cluster_zero: RMSD为0.00的聚类编号列表（最多100个）
            - Energy_s: 所有聚类的结合能量列表
    
    处理流程:
        1. 定位RMSD表格起始位置
        2. 解析表格数据，提取RMSD为0.00的聚类
        3. 遍历所有MODEL，提取结合能量值
        4. 限制聚类数量不超过100个
    
    用途:
        用于从对接结果中筛选与天然结合姿态最相似的姿态，
        以及获取这些姿态的结合亲和力预测值。
    """
    RMSD_TABLE = '_____|______|______|___________|_________|______'
    line_s = []
    Energy_s = []
    with open(dlg) as in_dlg:
        rmsd_table_gate = False
        energy_table_gate = False
        for line in in_dlg:
            if line.startswith(RMSD_TABLE):
                rmsd_table_gate = True
                continue
            if rmsd_table_gate and line.strip() == '':
                break
            if rmsd_table_gate:
                line_s.append(line)
            if line.startswith('DOCKED: MODEL'):
                energy_table_gate = True
                energy = []
            if line.startswith('DOCKED: ENDMDL'):
                energy_table_gate = False
                Energy_s.append(energy[0])
            if energy_table_gate and line.startswith('DOCKED: USER'):
                if 'Energy' in line:
                    energy.append(float(line.split('=')[1].split()[0]))

    cluster_zero = [
        int(line.split()[2]) for line in line_s if line.split()[4].strip() == '0.00'
    ]
    if len(cluster_zero) > 100:
        cluster_zero = cluster_zero[:100]
    return cluster_zero, Energy_s


def PDBQT_native(dlg): # is not used, can be safely skipped
    """
    从AutoDock DLG文件中提取天然配体的结合能量
    
    解析AutoDock结果文件，提取天然配体（即实验结构中的配体姿态）
    在给定蛋白质环境中的结合能量评分。
    
    参数:
        dlg (str): AutoDock DLG结果文件路径
    
    返回:
        list: 天然配体的结合能量值列表
    
    处理流程:
        1. 搜索包含'INPUT-LIGAND-PDBQT'关键字的行
        2. 从这些行中提取'Energy='后的数值
        3. 删除前两个值（可能为头部信息）
        4. 返回剩余的能量值
    
    用途:
        用于获取天然配体在给定蛋白质环境中的理论结合能量，
        作为评估对接算法性能的基准参考值。
    """
    energy = []
    with open(dlg) as in_dlg:
        for line in in_dlg:
            if line.startswith('INPUT-LIGAND-PDBQT'):
                if 'Energy' in line:
                    energy.append(float(line.split('=')[1].split()[0]))
        del energy[0]
        del energy[0]

    return energy


def make_pocket(protein, ligand_data, distance=5):
    """
    根据配体位置生成蛋白质结合口袋
    
    该函数根据配体原子的位置，从完整的蛋白质结构中
    提取一定距离范围内的所有原子，形成结合口袋结构。
    
    参数:
        protein (str): 蛋白质PDB文件路径
        ligand_data (dict): 配体原子数据字典，包含原子坐标信息
        distance (float): 口袋画定的距离阈值（Å），默认5Å
    
    返回:
        str: 生成的口袋PDB格式字符串，包含头部和原子记录
    
    处理流程:
        1. 解析蛋白质PDB文件，提取所有原子信息
        2. 过滤水分子(HOH)和氢原子
        3. 计算每个蛋白质原子到配体的最短距离
        4. 选择距离小于阈值的所有原子
        5. 组织成新的PDB格式字符串
    
    特征:
        - 自动处理替代位置（altLoc），优先选择A构型
        - 保持原始的残基编号和链标识
        - 重新编号原子序列号，确保连续性
    """
    protein_atom = []
    pocket = f"HEADER    {protein.split('/')[-1][:-7]}_POCKET\nCOMPND    {protein.split('/')[-1][:-7]}_POCKET\n"
    for line in open(protein).readlines():
        if line.startswith('ATOM  ') or line.startswith('HETATM'):
            record = line[0:6]  # 0
            serial = int(line[6:11])
            atomName = line[12:16].rstrip()  # 1
            altLoc = line[16]
            resName = line[17:20].strip()  # 2
            chainID = line[21]  # 3
            if chainID == ' ':
                chainID = 'A'
            resSeq = int(line[22:26])  # 4
            x = float(line[30:38])  # 5
            y = float(line[38:46])  # 5
            z = float(line[46:54])  # 5
            occupancy = line[54:60]  # 6
            tempFactor = line[60:66]  # 7
            element = line[66:78]  # 8
            if resName != 'HOH' and not line.strip().endswith('H'):
                if altLoc == ' ' or altLoc == 'A':
                    protein_atom.append(
                        [
                            record,
                            atomName,
                            resName,
                            chainID,
                            resSeq,
                            [x, y, z],
                            occupancy,
                            tempFactor,
                            element,
                        ]
                    )

    ligand_xyz = [data[2] for data in [*ligand_data.values()]]

    inner_atom = []
    for atom in protein_atom:
        atom_min_dis = 12
        for xyz in ligand_xyz:
            if atom_min_dis >= math.dist(atom[5], xyz):
                atom_min_dis = math.dist(atom[5], xyz)
        if atom_min_dis <= distance:
            # inner_atom.append(atom[4])
            # inner_chain.append(atom[3])
            inner_atom.append([atom[4], atom[3]])
    inner_atom_set = []
    for atom in inner_atom:
        if not atom in inner_atom_set:
            inner_atom_set.append(atom)
    # inner_atom_set = set(inner_atom)
    # if not inner_chain:
    #    return
    # inner_chain_set = max(set(inner_chain), key=inner_chain.count)
    serial_num = 0
    for atom in protein_atom:
        if [atom[4], atom[3]] in inner_atom_set:
            # if atom[4] in inner_atom_set and atom[3] in inner_chain_set:
            serial_num += 1
            #               0:6    6:11                 12:16       17:21         21     22:26              30:38        38:46          46:54
            pocket += f'{atom[0]}{serial_num: >5} {atom[1]: <4} {atom[2]: <4}{atom[3]}{atom[4]: >4}    {atom[5][0]: >8}{atom[5][1]: >8}{atom[5][2]: >8}{atom[6]}{atom[7]}{atom[8]}\n'
    pocket += 'END'
    return pocket


def pdb_list_cut(ligand_pdb, output):
    """
    将包含多个MODEL的PDB文件分割为单个分子文件
    
    该函数处理包含多个分子结构的PDB文件，将每个MODEL块
    保存为独立的PDB文件，并生成相应的索引文件。
    
    参数:
        ligand_pdb (str): 输入的多分子PDB文件路径
        output (str): 输出目录路径
    
    返回:
        list: 包含[name, file_path]的列表，每个元素代表一个分子
    
    处理流程:
        1. 使用read_pdb_block_from_pdb函数读取每个MODEL块
        2. 从每个块中提取分子名称（COMPND或TITLE行）
        3. 将每个块保存为独立的PDB文件
        4. 生成ligand_list.tsv索引文件
    
    输出文件:
        - Ligand_N.pdb: 单个分子PDB文件
        - ligand_list.tsv: 分子文件列表，格式为'文件路径 分子名'
    """
    pdbs = []
    fw = open(f'{output}/ligand_list.tsv', 'w')
    for idx, line in enumerate(read_pdb_block_from_pdb(ligand_pdb), 1):
        name = ''
        for i in line.split('\n'):
            if i.startswith('COMPND') or i.startswith('TITLE'):
                name = '_'.join(i.split()[1:])
                break
        if not name:
            name = f'ligand_{idx}'
        with open(f'{output}/Ligand_{idx}.pdb', 'w') as f:
            f.write(line)
            f.write('END\n')
        pdbs.append([name, f'{output}/Ligand_{idx}.pdb'])

        fw.write(f'{output}/Ligand_{idx}.pdb {name}\n')

    fw.close()

    return pdbs


def sdf_list_cut(ligand_sdf, output):
    """
    将SDF文件中的多个分子转换为单独的PDB文件
    
    该函数使用RDKit解析SDF文件中的所有分子，将每个分子
    转换为PDB格式并保存为独立文件，同时生成索引文件。
    
    参数:
        ligand_sdf (str): 输入的SDF文件路径
        output (str): 输出目录路径
    
    返回:
        list: 包含[name, file_path]的列表，每个元素代表一个分子
    
    处理流程:
        1. 使用RDKit的SDMolSupplier读取SDF文件
        2. 对每个分子添加氢原子（保持坐标）
        3. 提取分子名称（_Name属性）
        4. 将分子转换为PDB格式并保存
        5. 生成ligand_list.tsv索引文件
    
    特征:
        - 保留所有氢原子 (removeHs=False)
        - 自动跳过无法解析的分子
        - 为无名分子自动生成名称
    """
    pdbs = []
    fw = open(f'{output}/ligand_list.tsv', 'w')
    for idx, mol in enumerate(Chem.SDMolSupplier(ligand_sdf, removeHs=False), 1):
        name = ''
        if not mol:
            continue
        mol = Chem.AddHs(mol, addCoords=True)
        if mol.GetProp('_Name'):
            name = mol.GetProp('_Name')
        if not name:
            name = f'ligand_{idx}'
        with open(f'{output}/Ligand_{idx}.pdb', 'w') as f:
            f.write(Chem.MolToPDBBlock(mol))

        pdbs.append([name, f'{output}/Ligand_{idx}.pdb'])
        fw.write(f'{output}/Ligand_{idx}.pdb {name}\n')
    fw.close()

    return pdbs

def mol2_list_cut(ligand_mol2, output):
    """
    将MOL2文件中的多个分子转换为单独的PDB文件
    
    该函数使用Mol22mol函数解析MOL2文件中的所有分子，
    将每个分子转换为PDB格式并生成相应的文件路径列表。
    
    参数:
        ligand_mol2 (str): 输入的MOL2文件路径
        output (str): 输出目录路径
    
    返回:
        list: 包含[name, file_path]的列表，每个元素代表一个分子
    
    处理流程:
        1. 使用Mol22mol函数解析MOL2文件
        2. 对每个分子添加氢原子（保持坐标）
        3. 提取分子名称（_Name属性）
        4. 生成相应的PDB文件路径（但不实际写入文件）
    
    注意:
        该函数当前的实现中没有实际写入PDB文件或生成索引文件，
        只是返回了分子信息列表。可能需要进一步完善。
    """
    pdbs = []
    #fw = open(f'{output}/ligand_list.tsv', 'w')
    mols = Mol22mol(ligand_mol2)
    for idx, mol in enumerate(mols, 1):
        name = ''
        if not mol:
            continue
        mol = Chem.AddHs(mol, addCoords=True)
        if mol.GetProp('_Name'):
            name = mol.GetProp('_Name')
        if not name:
            name = f'ligand_{idx}'
        #with open(f'{output}/Ligand_{idx}.pdb', 'w') as f:
        #    f.write(Chem.MolToPDBBlock(mol))

        pdbs.append([name, f'{output}/Ligand_{idx}.pdb'])
        #fw.write(f'{output}/Ligand_{idx}.pdb {name}\n')
    #fw.close()

    return pdbs
        

def pdb_dock(recep, output, center=False, ncpu=8, gpus='0'):
    """
    执行分子对接并提取结合亲和力得分
    
    该函数调用gnss_dock2工具对多个配体进行对接计算，
    并从结果文件中提取每个配体的结合亲和力得分。
    
    参数:
        recep (str): 受体蛋白质PDB文件路径
        output (str): 输出目录路径
        center (str/bool): 对接中心坐标，为False时使用默认值
        ncpu (int): 使用的CPU核心数，默认8
        gpus (str): GPU设备ID，默认'0'
    
    返回:
        dict: 配体ID到结合亲和力得分的映射字典
    
    处理流程:
        1. 构建gnss_dock2命令行参数
        2. 执行对接计算
        3. 解析输出TSV文件，获取对接结果信息
        4. 从每个DLG文件中提取结合亲和力值
    
    注意:
        当前实现中对接命令被注释了，只进行结果解析部分。
        需要安装和配置gnss_dock2工具才能正常使用。
    """
    scores = {}
    #for line in open(f'{output}/preprocess/ligand_list.tsv'):
    #    box = line.split()[0]
    #    break
    #if center:
    #    command = [
    #        'gnss_dock2',
    #        '--label',
    #        'DOCK2',
    #        '--output',
    #        f'{output}/preprocess/autodock',
    #        '-l',
    #        f'{output}/preprocess/ligand_list.tsv',
    #        '--overlab',
    #        'True',
    #        '-r',
    #        f'{recep}',
    #        '--box_center',
    #        f'{center}',
    #        '--run_per_ligand',
    #        '1',
    #        '--ncpu',
    #        f'{ncpu}',
    #        '--gpu_id_s',
    #        f'{gpus}',
    #    ]
    #else:
    #    command = [
    #        'gnss_dock2',
    #        '--label',
    #        'DOCK2',
    #        '--output',
    #        f'{output}/preprocess/autodock',
    #        '-l',
    #        f'{output}/preprocess/ligand_list.tsv',
    #        '--overlab',
    #        'True',
    #        '-r',
    #        f'{recep}',
    #        '--box_center',
    #        f'{box}',
    #        '--run_per_ligand',
    #        '1',
    #        '--ncpu',
    #        f'{ncpu}',
    #        '--gpu_id_s',
    #        f'{gpus}',
    #    ]
    #p = subprocess.call(command)
    #if p != 0:
    #    pass

    df = pd.read_csv(f'{output}/preprocess/autodock/DOCK2.AK_Dock.extend.tsv', sep='\t')
    for rec in df.itertuples():
        dlg = f'{output}/preprocess/autodock/' + rec.DOCK_SDF[:-4] + '.dlg'
        for i in open(dlg):
            if i.startswith(
                'INPUT-LIGAND-PDBQT: USER    Estimated Free Energy of Binding '
            ):
                scores[rec.TID] = float(i.split('=')[1].split()[0])

    return scores


def preprocess(ligand_path, output='.', center=False):
    """
    预处理多种格式的配体文件，将其转换为单个PDB文件
    
    该函数是数据预处理的主入口，支持多种常见的分子文件格式，
    包括PDB、SDF、MOL2以及各种压缩格式，将其统一转换为单个PDB文件。
    
    参数:
        ligand_path (str): 输入配体文件路径
        output (str): 输出目录路径，默认为当前目录
        center (bool): 是否计算配体的几何中心，默认False
    
    返回:
        tuple: 包含两个元素
            - ligands: 配体信息列表，每个元素为[name, file_path]
            - center: 配体的几何中心坐标字符串（如果计算）
    
    支持的文件格式:
        - .pdb, .pdb.gz: 单个或多个MODEL的PDB文件
        - .sdf: 结构数据文件
        - .mol2: Tripos分子文件
        - .tar.gz, .tar: 压缩的PDB文件集合
        - .zip: ZIP压缩的PDB文件集合
        - .mae, .maegz: Schrödinger格式文件
    
    输出文件:
        - preprocess/Ligand_N.pdb: 单个配体PDB文件
        - preprocess/ligand_list.tsv: 配体文件索引列表
    
    特殊功能:
        当center=True时，计算所有配体原子的几何中心坐标，
        可用于对接计算中的搜索区域定义。
    """
    output = output + '/preprocess'
    if not os.path.isdir(output):
        os.makedirs(output)

    if ligand_path.endswith(('pdb', 'pdb.gz')):
        ligands = pdb_list_cut(ligand_path, output)

    elif ligand_path.endswith('.sdf'):
        ligands = sdf_list_cut(ligand_path, output)

    elif ligand_path.endswith('.mol2'):
        ligands = mol2_list_cut(ligand_path, output)

    elif ligand_path.endswith(('tar.gz', 'tar')):
        ligands = []
        tar = tarfile.open(ligand_path, 'r:*')
        file_names = tar.getnames()
        with open(f'{output}/ligand_list.tsv', 'w') as f:
            for idx, name in enumerate(file_names, 1):
                with tar.extractfile(name) as z_file:
                    f_read = z_file.read()
                with open(f'{output}/Ligand_{idx}.pdb', 'w') as fw:
                    fw.write(f_read.decode('utf-8'))
                ligands.append([name, f'{output}/Ligand_{idx}.pdb'])
                f.write(f'{output}/Ligand_{idx}.pdb {name[:-4]}\n')
        tar.close()

    elif ligand_path.endswith('zip'):
        ligands = []
        with open(f'{output}/ligand_list.tsv', 'w') as f:
            with zipfile.ZipFile(ligand_path) as z:
                z_list = z.namelist()
                for idx, z_name in enumerate(z_list, 1):
                    name = os.path.split(z_name)[1]
                    with z.open(z_name) as z_file:
                        f_read = z_file.read()
                    with open(f'{output}/Ligand_{idx}.pdb', 'w') as fw:
                        fw.write(f_read.decode('utf-8'))
                    ligands.append([name, f'{output}/Ligand_{idx}.pdb'])
                    f.write(f'{output}/Ligand_{idx}.pdb {name[:-4]}\n')

    elif ligand_path.endswith(('mae', 'maegz')):
        ligands = []
        command = [
            f'{os.environ["SCHRODINGER"]}/utilities/pdbconvert',
            '-n',
            '2:',
            '-imae',
            f'{ligand_path}',
            '-opdb',
            f'{output}/ligand.pdb',
        ]
        logger.info(command)
        p = subprocess.call(command)
        if p != 0:
            pass

        f = open(f'{output}/ligand_list.tsv', 'w')
        p_co = re.compile('^ligand-[0-9]+.pdb$')
        pdb_files = list(filter(p_co.match, os.listdir(output)))
        for idx in range(1, len(pdb_files) + 1):
            pdb_file = f'{output}/ligand-{idx}.pdb'
            lig_id = f'Ligand_{idx+1}'
            name = ''
            for i in open(pdb_file):
                if i.startswith('COMPND') or i.startswith('TITLE'):
                    name = ' '.join(i.split()[1:])
                    break
            if name == 'Displayed atoms':
                continue

            if not name:
                name = f'Ligand_{idx+1}'
            if not f'{output}/{lig_id}.pdb' in ligands:
                shutil.copyfile(pdb_file, f'{output}/{lig_id}.pdb')
                ligands.append([name, f'{output}/{lig_id}.pdb'])
            os.remove(pdb_file)
            f.write(f'{output}/{lig_id}.pdb {name}\n')
        if not ligands:
            ligands.append(['ligand', f'{output}/ligand.pdb'])
            f.write(f'{output}/ligand.pdb lignad\n')
        f.close()

    else:
        logger.info('not have pdb or gz or zip file')
        sys.exit()
    if center:
        x, y, z = 0, 0, 0
        all_len = 0
        for ligand in open(f'{output}/ligand_list.tsv'):
            for line in open(ligand.split()[0]):
                if line.startswith('ATOM') or line.startswith('HETATM'):
                    x += float(line[30:38])
                    y += float(line[38:46])
                    z += float(line[46:54])
                    all_len += 1

        center = f'{x/all_len},{y/all_len},{z/all_len}'

    return ligands, center


def make_graph(protein_path, ligand_path, scores, dock=True, distance=5):
    """
    为单个蛋白质-配体复合物生成图数据对象
    
    该函数是对mol_2_graph函数的封装，处理单个蛋白质-配体复合物，
    生成包含结合亲和力得分的完整PyG图数据对象。
    
    参数:
        protein_path (str): 蛋白质PDB文件路径
        ligand_path (list/str): 配体信息，可以是列表[name, path]或字符串路径
        scores (dict): 配体ID到结合亲和力得分的映射
        dock (bool): 是否使用对接结果，默认True
        distance (float): 口袋生成的距离阈值，默认5Å
    
    返回:
        tuple: 包含四个元素
            - dmp: PyG Data对象，包含图结构和特征
            - error: 错误标志 (1=成功, 0=失败)
            - score: 结合亲和力得分（失败时为1000.0）
            - name: 配体名称
    
    处理流程:
        1. 从配体路径或文件内容中提取配体ID
        2. 如果不是对接模式，先生成PDBQT文件
        3. 调用mol_2_graph函数生成图数据
        4. 创建PyG Data对象，包含配体名称
        5. 检查结合亲和力得分的有效性
    
    错误处理:
        当配体ID在scores中不存在或图构建失败时，
        设置error=0并返回默认值1000.0作为结合亲和力。
    """
    ligand_id = None
    if isinstance(ligand_path, list) and ligand_path[1].endswith('pdb'):
        ligand_id = os.path.splitext(os.path.split(ligand_path[1])[1])[0]
    elif isinstance(ligand_path, list):
        for line in ligand_path[1].split('\n'):
            if line.startswith('REMARK  Lig_num'):
                ligand_id = line.split('=')[1].strip()
    else:
        # 处理字符串路径的情况
        ligand_id = os.path.splitext(os.path.split(ligand_path)[1])[0]
    
    if not ligand_id:
        ligand_id = ligand_path[0] if isinstance(ligand_path, list) else 'unknown'

    if not dock:
        # 修复PDBQT文件生成逻辑
        actual_path = ligand_path[1] if isinstance(ligand_path, list) else ligand_path
        mol = pdb2mol(actual_path, False)
        ligand_pdbqt = rdkit_mol_TO_pdbqt(mol)
        if ligand_pdbqt:
            with open(actual_path + 'qt', 'w') as f:
                f.write(ligand_pdbqt)
    if isinstance(ligand_path, list):
        result = mol_2_graph(ligand_path[1], protein_path, distance)
    else:
        result = mol_2_graph(ligand_path, protein_path, distance)
    node_attrs, edge_attrs, edge_indexs, error = result
    dmp = Data(
        x=node_attrs, edge_index=edge_indexs, edge_attr=edge_attrs, name=ligand_id
    )
    if not ligand_id in scores and not ligand_id[:-5] in scores:
        error = 0
    if error == 0:
        return dmp, error, 1000.0, ligand_path[0] if isinstance(ligand_path, list) else 'unknown'
    # if ligand_id[:-5] in scores:
    #    return dmp, error, scores[ligand_id[:-5]]
    return dmp, error, scores[ligand_id], ligand_path[0] if isinstance(ligand_path, list) else 'unknown'


if __name__ == '__main__':
    import argparse
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    ch.setFormatter(logging.Formatter(LOG_FORMAT))
    logger.addHandler(ch)

    logger.setLevel(logging.INFO)
    parser = argparse.ArgumentParser()
    parser.add_argument('-r', '--receptor', help='receptor pdb')
    parser.add_argument('-l', '--ligands', help='pdb or pdb.tar(.gz) or pdb.zip')
    parser.add_argument('-o', '--output', help='output')
    parser.add_argument('-b', '--bind_aff', help='binding affinity file')
    parser.add_argument('-c', '--center', help='center (True) or first(False) ')
    parser.add_argument('--ncpu', help='number of cpu')
    parser.add_argument('--gpu', help='gpu ids')
    args = parser.parse_args()

    pdb = args.receptor
    ligands = args.ligands
    output = args.output if args.output != None else '.'
    ligands, center = preprocess(ligands, output, center=False)
    if args.bind_aff:
        scores = {}
        df = pd.read_csv(args.bind_aff, sep='\t')
        if ligands[0][1].endswith('ligand_1.pdb'):
            for idx, row in enumerate(df.itertuples()):
                scores[f'Ligand_{idx+1}'] = row.BINDING_E
        else:
            for row in df.itertuples():
                scores[row.UID] = row.BINDING_E
        logger.info(f'从 {args.bind_aff} 中读取了 {len(scores)} 个绑定亲和力得分')
    else:
        # 如果没有提供绑定亲和力文件，生成统一的假数据（+10.0）
        scores = {}
        for idx, ligand in enumerate(ligands, 1):
            ligand_id = f'Ligand_{idx}'
            scores[ligand_id] = 10.0  # 统一设置为 +10.0
        logger.info(f'以on-the-fly方式生成了 {len(scores)} 个统一的假亲和力得分，所有值均为 +10.0')
    dmps = []
    for ligand in ligands:
        dmp = make_graph(pdb, ligand, scores, True)
        dmps.append(dmp)

    with open(f'{output}/dlg_5A_2.pkl', 'wb') as f:
        pickle.dump(dmps,f) 
