# Construct PyG Graph AutoDock 脚本设计文档

## 概述

`construct_pyg_graph_autodock.py` 是一个专门用于分子对接和图神经网络数据预处理的Python脚本。该脚本主要功能是将蛋白质-配体复合物转换为PyTorch Geometric (PyG) 图数据结构，用于后续的机器学习模型训练和预测。

## 技术栈与依赖

### 核心依赖库
- **RDKit**: 化学信息学库，用于分子处理和特征提取
- **PyTorch Geometric**: 图神经网络框架
- **Meeko**: PDBQT格式分子处理
- **OpenBabel**: 分子格式转换
- **NumPy**: 数值计算
- **Pandas**: 数据处理

### 支持的分子格式
- PDB (蛋白质数据银行格式)
- PDBQT (AutoDock格式)
- SDF (结构数据文件)
- MOL2 (Tripos分子格式)
- MAE/MAEGZ (Schrödinger格式)

### PDBQT中间格式转换机制

#### 统一转换策略
无论输入什么格式的配体文件（SDF、MOL2、PDB等），脚本内部都会将其转换为PDBQT格式进行处理。这种设计确保了特征提取的一致性和准确性。

#### 不同格式的转换路径

**SDF格式转换流程**:
```python
# SDF → RDKit分子对象 → PDBQT字符串 → Meeko对象
sdf_file → sdf2mol() → rdkit_mol_TO_pdbqt() → PDBQTMolecule()
```

**MOL2格式转换流程**:
```python
# MOL2 → RDKit分子对象 → PDB文件 → PDBQT格式
mol2_file → Mol22mol() → PDB文件 → PDBQT处理
```

**PDB格式转换流程**:
```python
# PDB → 添加PDBQT扩展名 → Meeko对象
pdb_file → ligand_path + 'qt' → PDBQTMolecule.from_file()
```

#### PDBQT转换的必要性

1. **原子类型标准化**
   - PDBQT格式包含AutoDock特定的原子类型（如A, C, HD, OA等）
   - 这些标准化的原子类型对图特征化至关重要
   - 确保所有分子使用相同的原子类型体系

2. **电荷信息获取**
   - PDBQT格式中包含Gasteiger部分电荷信息
   - 电荷数据用于计算原子的疏水性特征
   - 提供更准确的分子间相互作用预测

3. **旋转键识别**
   - Meeko库在转换过程中自动识别和标记旋转键
   - 旋转键信息对于分子柔性建模很重要
   - 影响对接结果的准确性

4. **几何优化与验证**
   - 转换过程中会进行分子几何合理性检查
   - 自动修正不合理的键长和键角
   - 确保3D结构的化学合理性

#### 关键转换函数

- **`rdkit_mol_TO_pdbqt(mol, config=None)`**
  - 使用Meeko库将RDKit分子转换为PDBQT格式
  - 包含旋转键分析和原子类型分配
  - 处理分子的柔性和刚性部分

- **`poss2data(poss_mols, clus_list=None)`**
  - 从PDBQT分子对象中提取图构建所需的数据
  - 结合PDBQT和RDKit信息获取完整原子属性
  - 包含坐标、电荷、原子类型和编号信息

- **`PDBQTMolecule`类（来自Meeko库）**
  - 专门处理PDBQT格式的分子对象
  - 支持多种输入方式：文件路径、字符串、DLG文件
  - 提供与RDKit的双向转换接口

#### 转换带来的优势

1. **统一的特征提取流程**
   - 所有分子经过相同的预处理管道
   - 消除不同输入格式带来的特征差异
   - 提高模型训练的稳定性

2. **与AutoDock生态系统兼容**
   - 保持与AutoDock对接工具的完全兼容
   - 支持对接结果的直接导入和分析
   - 便于与现有对接工作流集成

3. **增强的化学信息**
   - 获得更丰富的原子和键的化学属性
   - 支持复杂的分子间相互作用建模
   - 提供更准确的图神经网络输入特征

#### 错误处理机制

转换过程包含多层错误处理：
- **格式识别失败**: 自动尝试备用解析方法
- **转换异常**: 使用OpenBabel作为备用转换工具
- **数据提取失败**: 返回零填充的默认张量，确保程序稳定运行

## 系统架构

### 主要组件

```mermaid
graph TD
    A[输入文件] --> B[文件格式识别]
    B --> C[分子预处理]
    C --> D[特征化器Featurize]
    D --> E[图构建]
    E --> F[PyG数据对象]
    
    D --> D1[原子节点特征]
    D --> D2[共价键边特征]
    D --> D3[非共价键边特征]
    
    E --> E1[蛋白质图]
    E --> E2[配体图]
    E --> E3[蛋白质-配体交互图]
```

### 数据流处理

1. **输入阶段**: 接收蛋白质和配体文件
2. **预处理阶段**: 格式转换和分子准备
3. **特征提取阶段**: 生成原子和键的特征向量
4. **图构建阶段**: 创建图网络表示
5. **输出阶段**: 生成PyG数据对象

## 核心功能模块

### 重要设计差异说明

本脚本与训练版本(`input_module_my-training.py`)在关键函数实现上存在重要差异，这些差异反映了不同的应用场景需求：

#### `poss2data`函数差异

**AutoDock版本（本脚本）**:
```python
def poss2data(poss_mols, clus_list=None):
    # 只处理第一个分子构象
    for i_mol, mol in enumerate(poss_mols):
        # 处理逻辑...
        break  # 关键：只处理第一个
    return mol, data  # 返回单个分子数据
```

**Training版本**:
```python
def poss2data(poss_mols, clus_list=None):
    # 处理多个分子构象
    for i_mol, mol in enumerate(poss_mols):
        if clus_list == None or i_mol+1 in clus_list:
            # 处理多个构象...
    return lig_mol, datas, dock_score  # 返回列表数据
```

#### `mol_2_graph`函数差异

**AutoDock版本（本脚本）**:
- **单个配体处理**: 只生成一个图数据对象
- **错误处理**: 失败时返回预定义的零张量
- **返回格式**: `(node_attrs, edge_attrs, edge_indexs, error)`
- **性能优化**: 专注于速度，适合实时预测

**Training版本**:
- **批量处理**: 处理多个配体构象和姿态
- **数据完整性**: 包含天然构象、对接构象、诱骗分子
- **返回格式**: `(node_attrs_list, edge_attrs_list, edge_indexs_list, ligand_dock)`
- **训练标签**: 包含RMSD和结合亲和力标签

#### 应用场景区别

| 特性 | AutoDock版本 | Training版本 |
|------|-------------|-------------|
| **主要用途** | 实时预测/推理 | 训练数据生成 |
| **处理模式** | 单个配体快速处理 | 批量多构象处理 |
| **数据结构** | 单个Data对象 | Data对象列表 |
| **错误策略** | 零张量填充 | 部分失败容忍 |
| **性能重点** | 速度优化 | 数据完整性 |
| **应用环境** | 生产环境预测 | 模型训练准备 |

#### 设计原理

1. **AutoDock版本优势**:
   - 快速响应，适合在线服务
   - 内存占用少，适合大规模筛选
   - 错误处理简洁，系统稳定性高

2. **Training版本优势**:
   - 数据多样性丰富，提高模型泛化能力
   - 包含完整的训练标签信息
   - 支持复杂的数据增强策略

这种差异化设计确保了同一套核心算法能够适应不同阶段的应用需求。

### 1. 文件读取与格式转换

#### 功能描述
处理多种分子文件格式，统一转换为RDKit分子对象。

#### 关键函数
- `read_pdb_block_from_pdb()`: 读取PDB文件中的MODEL块
- `Mol22mol()`: MOL2格式转换
- `sdf2mol()`: SDF格式读取
- `pdb2mol()`: PDB格式转换

#### 输入输出
- **输入**: 各种格式的分子文件
- **输出**: RDKit Mol对象

### 2. 分子特征化 (Featurize类)

#### 2.1 原子节点特征提取

**特征维度**: 73维向量

**特征组成**:
- 原子符号 (20维): C, H, O, N, S, P, F, Cl, Br, I, B, Se, Fe, Ru, Mn, Co, Ni, Cu, Zn, other
- 原子度数 (7维): 0-6
- 氢原子数量 (5维): 0-4
- 隐式价 (6维): 0-5
- 显式价 (6维): 0-5
- 显式氢数 (2维): 0-1
- 隐式氢数 (5维): 0-4
- 杂化类型 (5维): SP, SP2, SP3, SP3D, SP3D2
- 形式电荷 (7维): -2到+4
- 自由基电子数 (3维): 0-2
- 芳香性 (1维): 布尔值
- 环状态 (1维): 布尔值
- 疏水性 (1维): 基于原子类型
- 氢键供体 (1维): 布尔值
- 氢键受体 (1维): 布尔值
- 酸性 (1维): 布尔值
- 碱性 (1维): 布尔值

#### 2.2 边特征提取

**特征维度**: 24维向量

**特征组成**:
- 蛋白质-配体键类型 (3维): 蛋白质内键/配体内键/蛋白质-配体键
- 键类型 (4维): 单键/双键/三键/芳香键
- 立体化学 (6维): 不同立体构型
- 环状态 (1维): 是否在环中
- 共轭状态 (1维): 是否共轭
- 特殊键类型 (5维): 疏水键/氢键/离子键等
- 距离分档 (4维): <2Å, <4Å, <6Å, <8Å

### 3. 口袋生成模块

#### 功能描述
基于配体位置生成蛋白质结合口袋，用于图构建。

#### 关键函数
- `make_pocket()`: 生成结合口袋

#### 算法逻辑
1. 读取蛋白质原子坐标
2. 计算每个蛋白质原子到配体的最小距离
3. 选择距离小于阈值(默认5Å)的原子
4. 生成口袋PDB格式字符串

### 4. 图构建核心 (mol_2_graph函数)

#### 输入参数
- `ligand_path`: 配体文件路径
- `protein_file`: 蛋白质文件路径  
- `distance`: 口袋生成距离阈值
- `clus_list`: 聚类列表(可选)

#### 处理流程

```mermaid
flowchart TD
    A[配体文件] --> B[格式识别与转换]
    B --> C[生成PDBQT格式]
    C --> D[提取配体数据]
    
    E[蛋白质文件] --> F[生成结合口袋]
    F --> G[提取口袋数据]
    
    D --> H[配体特征化]
    G --> I[口袋特征化]
    
    H --> J[距离矩阵计算]
    I --> J
    
    J --> K[边构建]
    K --> L[PyG数据对象]
```

#### 输出结果
- `node_attrs`: 节点特征张量 (N×73)
- `edge_attrs`: 边特征张量 (M×24) 
- `edge_indexs`: 边索引张量 (2×M)
- `error`: 错误标志

### 5. 预处理模块

#### 批量文件处理

**支持的输入格式**:
- 单个PDB文件
- 多MODEL PDB文件
- SDF文件
- MOL2文件
- 压缩包(TAR.GZ, ZIP)
- Schrödinger MAE文件

**处理函数**:
- [pdb_list_cut()](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L1863-L1906): PDB文件分割
- [sdf_list_cut()](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L1909-L1953): SDF文件分割
- [mol2_list_cut()](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L1955-L1998): MOL2文件处理
- [preprocess()](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L2092-L2224): 统一预处理入口

#### 图数据生成

**核心函数**:
- [make_graph()](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L2227-L2295): 为单个蛋白质-配体复合物生成图数据对象

**[make_graph](file:///Users/<USER>/Desktop/casf-2016/construct_pyg_graph_autodock.py#L2227-L2295)函数详细说明**:
- **输入**: 蛋白质路径、配体信息、结合亲和力得分字典、对接标志、距离阈值
- **输出**: PyG Data对象、错误标志、结合亲和力得分、配体名称
- **特点**: 
  - 支持配体路径的多种格式（列表或字符串）
  - 自动提取配体ID并匹配结合亲和力得分
  - 包含完整的错误处理机制
  - 失败时返回默认值1000.0作为结合亲和力

#### 分子对接集成

**对接流程**:
1. 配体列表生成
2. AutoDock调用(通过gnss_dock2)
3. 结合亲和力提取
4. 结果文件解析

**注意**: 当前版本中，如果没有提供结合亲和力文件，系统会生成统一的虚拟数据而非执行实际对接计算。

### 6. 辅助功能模块

#### 距离计算
- `calculateDistance()`: 高效的原子间距离矩阵计算

#### 格式转换
- `rdkit_mol_TO_pdbqt()`: RDKit分子转PDBQT
- `pdbqt2mol()`: PDBQT转RDKit分子
- `pdbqt2pdbfile()`: PDBQT转PDB文件

#### 能量提取
- `cluster_list()`: 从DLG文件提取聚类信息
- `PDBQT_native()`: 提取天然配体能量

## 数据结构定义

### 图节点表示
每个原子作为图的一个节点，包含73维特征向量描述原子的化学和几何性质。

### 图边表示
图中的边分为三类：
1. **蛋白质内部边**: 蛋白质原子间的共价键
2. **配体内部边**: 配体原子间的共价键  
3. **蛋白质-配体边**: 非共价相互作用(氢键、疏水作用、离子键等)

### PyG数据对象
最终输出的Data对象包含：
- `x`: 节点特征矩阵
- `edge_index`: 边连接信息
- `edge_attr`: 边特征矩阵
- `name`: 配体标识符

## 疏水性数据库

脚本内置了详细的蛋白质原子疏水性数据库(`prot_atom_hydrophobicity`)，包含：
- 20种标准氨基酸
- 修饰氨基酸
- 金属离子
- 溶剂分子

每个原子根据其氨基酸类型和原子名称分配疏水性值(0或1)。

## 命令行接口

### 参数说明
- `-r, --receptor`: 受体蛋白质PDB文件
- `-l, --ligands`: 配体文件(支持多种格式)
- `-o, --output`: 输出目录
- `-b, --bind_aff`: 结合亲和力文件
- `-c, --center`: 对接中心设置
- `--ncpu`: CPU核心数
- `--gpu`: GPU设备ID

### 使用示例
```
python construct_pyg_graph_autodock.py \
    -r receptor.pdb \
    -l ligands.sdf \
    -o output_dir \
    --ncpu 8 \
    --gpu 0
```

## 错误处理机制

### 异常捕获
- 分子解析失败时返回零填充张量
- 口袋生成失败时跳过处理
- 格式转换错误时使用备用方法

### 错误标志
- `error = 1`: 处理成功
- `error = 0`: 处理失败，返回默认值

## 输出格式

### 主要输出文件
- `dlg_5A_2.pkl`: 序列化的PyG数据对象列表
- `ligand_list.tsv`: 配体文件索引
- `autodock/`: 对接结果目录

### 数据结构
每个处理结果包含：
- PyG Data对象
- 错误状态
- 结合亲和力得分
- 配体名称

## 性能优化

### 计算优化
- 使用NumPy矢量化距离计算
- 批量处理多个配体
- 并行化分子对接

### 内存管理
- 流式处理大文件
- 及时释放临时对象
- 压缩存储格式

## 应用场景

1. **药物发现**: 虚拟筛选和先导化合物优化
2. **蛋白质工程**: 结合位点分析和设计
3. **机器学习**: 图神经网络训练数据准备
4. **结构生物学**: 蛋白质-配体相互作用研究

## 详细使用场景归纳

基于脚本的输入参数和支持的文件格式，该脚本有以下几种主要使用场景：

### 1. 基于配体文件格式的不同使用场景

#### 1.1 单个分子文件场景
- **PDB文件** (`.pdb`): 单个配体分子的标准PDB格式
- **压缩PDB文件** (`.pdb.gz`): 压缩的单个配体PDB文件
- **SDF文件** (`.sdf`): 单个配体的结构数据文件
- **MOL2文件** (`.mol2`): 单个配体的Tripos分子文件

#### 1.2 Multi-frame配体文件场景
- **Multi-frame PDB文件**: 包含多个MODEL块的PDB文件，每个MODEL代表不同的分子构象或不同的配体分子
- **Multi-frame MOL2文件**: 包含多个分子结构的MOL2文件，脚本会使用OpenBabel逐个读取每个分子
- **Multi-molecule SDF文件**: 包含多个分子的SDF文件，RDKit的SDMolSupplier会逐个处理每个分子

#### 1.3 压缩文件集合场景
- **TAR压缩包** (`.tar`, `.tar.gz`): 包含多个PDB文件的压缩包
- **ZIP压缩包** (`.zip`): 包含多个PDB文件的ZIP压缩包
- **Schrödinger格式** (`.mae`, `.maegz`): 需要Schrödinger软件环境，会自动转换为PDB格式

### 2. 基于结合亲和力数据来源的使用场景

#### 2.1 实验结合亲和力场景
- **使用 `-b/--bind_aff` 参数**: 提供包含实验测定结合亲和力的TSV文件
- 文件格式要求：包含`UID`（配体ID）和`BINDING_E`（结合能）列
- 适用于已知实验数据的训练集构建

#### 2.2 虚拟结合亲和力场景
- **不使用 `-b` 参数**: 在没有提供绑定亲和力文件时，生成统一的虚拟亲和力数据（+10.0）
- 所有配体分子将被分配相同的虚拟结合亲和力值
- 适用于仅需要图结构而不关心具体结合亲和力的应用场景
- 避免了复杂的对接计算，提高处理速度

### 3. 基于结合口袋定义的使用场景

#### 3.1 基于配体位置的口袋生成
- **默认模式**: 根据配体分子的位置，自动提取5Å范围内的蛋白质原子作为结合口袋
- 适用于已知配体结合位置的复合物结构

#### 3.2 自定义中心的口袋生成
- **使用 `-c/--center` 参数**: 手动指定对接中心坐标
- 格式：`x,y,z`坐标值
- 适用于已知结合位点但无配体参考结构的情况

### 4. 基于计算资源的使用场景

#### 4.1 CPU并行计算场景
- **使用 `--ncpu` 参数**: 指定CPU核心数进行并行对接计算
- 适用于大规模配体筛选任务

#### 4.2 GPU加速计算场景
- **使用 `--gpu` 参数**: 指定GPU设备ID进行加速计算
- 适用于需要快速处理大量配体的高通量筛选

### 5. 典型应用场景示例

#### 5.1 药物发现场景：Multi-frame MOL2虚拟筛选
```
python construct_pyg_graph_autodock.py \
  -r protein_target.pdb \
  -l virtual_library.mol2 \
  -o screening_results \
  --ncpu 16 --gpu 0
```
**特点**: MOL2文件包含化合物库中的多个分子，脚本会自动分割并对每个分子进行图构建

#### 5.2 结合模式分析场景：Multi-frame PDB构象分析
```
python construct_pyg_graph_autodock.py \
  -r protein.pdb \
  -l ligand_conformations.pdb \
  -o conformation_analysis \
  -b experimental_affinities.tsv
```
**特点**: PDB文件包含同一配体的多个构象（多个MODEL），使用实验结合亲和力数据

#### 5.3 高通量筛选场景：压缩文件集合处理
```
python construct_pyg_graph_autodock.py \
  -r target.pdb \
  -l compound_library.tar.gz \
  -o hts_results \
  -c "25.0,30.0,15.0" \
  --ncpu 32
```
**特点**: 处理压缩的化合物库文件，使用指定的对接中心坐标

#### 5.4 训练数据准备场景：已知复合物结构
```
python construct_pyg_graph_autodock.py \
  -r protein_complex.pdb \
  -l ligand_poses.sdf \
  -o training_data \
  -b binding_affinities.tsv
```
**特点**: 使用已知的蛋白质-配体复合物结构和实验结合亲和力数据构建训练集

### 6. 输出文件的使用场景

- **`dlg_5A_2.pkl`**: 包含所有图数据对象的pickle文件，用于机器学习模型训练
- **`preprocess/`目录**: 包含预处理后的单个配体PDB文件和索引文件
- **`preprocess/autodock/`目录**: 包含对接计算的详细结果文件

### 7. 文件格式适配性总结

| 文件格式 | 单分子支持 | 多分子支持 | 压缩支持 | 自动分割 | 特殊要求 |
|----------|------------|------------|----------|----------|-----------|
| PDB | ✓ | ✓ (MODEL块) | ✓ (.gz) | ✓ | 无 |
| SDF | ✓ | ✓ | ✗ | ✓ | 无 |
| MOL2 | ✓ | ✓ | ✗ | ✓ | OpenBabel |
| TAR/TAR.GZ | ✗ | ✓ | ✓ | ✓ | 包含PDB文件 |
| ZIP | ✗ | ✓ | ✓ | ✓ | 包含PDB文件 |
| MAE/MAEGZ | ✓ | ✓ | ✓ (.maegz) | ✓ | Schrödinger环境 |

### 8. 计算模式选择指南

#### 实验数据模式 (使用-b参数)
- **适用场景**: 已有实验结合亲和力数据
- **优势**: 计算速度快，使用真实实验数据
- **应用**: 训练数据准备、模型验证

#### 虚拟数据模式 (不使用-b参数)
- **适用场景**: 仅需要图结构，不关心具体结合亲和力值
- **特点**: 自动生成统一的虚拟亲和力数据（+10.0）
- **优势**: 无需对接计算，处理速度极快
- **应用**: 图结构分析、快速批量处理、结构特征提取

#### 对接计算模式（已废弃）
- **说明**: 早期版本支持的gnss_dock2对接计算功能
- **当前状态**: 代码中已注释，不再主动调用
- **替代方案**: 使用虚拟数据模式或提供预计算的结合亲和力文件
