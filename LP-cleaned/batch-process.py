# batch_processor.py (Version 2)

import os
import csv
import subprocess
import argparse
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import time

# --- 配置区 ---
# 日志文件名
COMPLETED_LOG_FILE = 'completed_pdbs.log'
ERROR_LOG_FILE = 'error.log'
# 你的数据预处理脚本的路径
# *** 请根据你的实际情况修改此路径 ***
DATA_PREP_SCRIPT = 'input_module_my-brain.py' 

def get_existing_pdbs(log_file):
    """从日志文件中读取已经处理过的PDB ID。"""
    if not os.path.exists(log_file):
        return set()
    with open(log_file, 'r') as f:
        # 使用 set 是为了更快的查找效率
        return {line.strip() for line in f if line.strip()}

def run_task(pdb_id):
    """
    为单个 PDB ID 运行数据处理脚本。
    这个函数将被每个子进程独立调用。
    
    返回:
        tuple: (pdb_id, status, message)
               status 可以是 'success', 'error'
    """
    # 构建完整的命令字符串，包括激活conda环境
    conda_cmd = 'eval "$(~/.miniconda3-1/bin/conda shell.bash hook)" && conda activate akscore2-1'
    python_cmd = f'python {DATA_PREP_SCRIPT} --input {pdb_id}'
    full_cmd = f'{conda_cmd} && {python_cmd}'
    
    try:
        # 使用shell=True来执行完整的命令字符串
        result = subprocess.run(
            full_cmd,
            shell=True,
            capture_output=True,
            text=True,
            check=False,  # 设置为False，这样即使命令失败也不会抛出异常
            executable='/bin/bash'  # 确保使用bash执行命令
        )

        # 检查返回码
        if result.returncode == 0:
            # 成功
            return (pdb_id, 'success', f"Successfully processed {pdb_id}.")
        else:
            # 失败
            error_message = f"--- PDB ID: {pdb_id} ---\n"
            error_message += f"Exit Code: {result.returncode}\n"
            error_message += f"STDOUT:\n{result.stdout.strip()}\n"
            error_message += f"STDERR:\n{result.stderr.strip()}\n"
            return (pdb_id, 'error', error_message)

    except Exception as e:
        # 捕获其他可能的异常 (例如，subprocess本身无法启动)
        return (pdb_id, 'error', f"--- PDB ID: {pdb_id} ---\nFailed to execute subprocess: {str(e)}\n")

def main():
    """主函数，用于解析参数和编排任务。"""
    parser = argparse.ArgumentParser(
        description="Batch process PDB data using multiple cores with resume and logging capabilities.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '--csv',
        required=True,
        help="Path to the input CSV file (e.g., CLEANED-collect.csv).\nIt must contain a header and a 'pdb_id' column."
    )
    parser.add_argument(
        '--workers',
        type=int,
        default=20,
        help=f"Number of parallel processes to use. (Default: {cpu_count()}, the number of CPU cores on this machine)"
    )
    # --- 新增功能: --limit 参数 ---
    parser.add_argument(
        '--limit',
        type=int,
        default=500, # 默认值为0，代表没有限制
        help="Maximum number of tasks to process in this single run.\nIf set to a positive number, the script will process up to this many new tasks and then exit.\n(Default: 0, means no limit)"
    )
    args = parser.parse_args()

    # 1. 从 CSV 加载所有需要处理的 PDB ID
    try:
        with open(args.csv, 'r', newline='') as f:
            reader = csv.DictReader(f)
            # 确保CSV文件中有 'pdb_id' 列
            if 'pdb_id' not in reader.fieldnames:
                print(f"Error: CSV file '{args.csv}' must contain a 'pdb_id' column.")
                return
            all_pdb_ids = {row['pdb_id'] for row in reader}
    except FileNotFoundError:
        print(f"Error: Cannot find the CSV file at '{args.csv}'")
        return
    except Exception as e:
        print(f"An error occurred while reading the CSV file: {e}")
        return
        
    print(f"Found {len(all_pdb_ids)} total PDB IDs in '{args.csv}'.")

    # 2. 加载已经完成的 PDB ID，实现断点续传
    completed_pdbs = get_existing_pdbs(COMPLETED_LOG_FILE)
    if completed_pdbs:
        print(f"Found {len(completed_pdbs)} PDBs already processed. Resuming...")
    
    # 3. 确定本次需要运行的任务列表
    pdbs_to_process_full = sorted(list(all_pdb_ids - completed_pdbs))

    # --- 新增功能: 应用 --limit 限制 ---
    if args.limit > 0 and len(pdbs_to_process_full) > args.limit:
        print(f"INFO: There are {len(pdbs_to_process_full)} tasks remaining, but this run is limited to {args.limit} by the --limit flag.")
        pdbs_to_process = pdbs_to_process_full[:args.limit]
    else:
        pdbs_to_process = pdbs_to_process_full

    if not pdbs_to_process:
        print("All PDBs have already been processed. Nothing to do.")
        return

    print(f"Starting processing for {len(pdbs_to_process)} new PDBs with {args.workers} worker(s).")
    print(f"Completed tasks will be logged to: {COMPLETED_LOG_FILE}")
    print(f"Errors will be logged to: {ERROR_LOG_FILE}")
    print("-" * 50)
    time.sleep(2) # 等待2秒让用户可以阅读信息

    # 4. 使用 multiprocessing.Pool 创建进程池
    # 使用 'a' 模式追加写入日志文件
    completed_log = open(COMPLETED_LOG_FILE, 'a')
    error_log = open(ERROR_LOG_FILE, 'a')

    try:
        with Pool(processes=args.workers) as pool:
            # 使用 imap_unordered 来获取结果，它可以在任务完成时立即返回结果，便于更新进度条
            results_iterator = pool.imap_unordered(run_task, pdbs_to_process)
            
            # 使用 tqdm 创建进度条
            pbar = tqdm(results_iterator, total=len(pdbs_to_process), desc="Processing PDBs")
            
            for pdb_id, status, message in pbar:
                if status == 'success':
                    # 记录成功
                    completed_log.write(f"{pdb_id}\n")
                    completed_log.flush() # 立即写入磁盘，防止程序崩溃时丢失
                else: # status == 'error'
                    # 记录失败
                    error_log.write(f"{message}\n")
                    error_log.flush()
                    # 可以在进度条上显示当前出错的ID
                    pbar.set_postfix_str(f"ERROR: {pdb_id}")

    finally:
        # 确保文件句柄被关闭
        completed_log.close()
        error_log.close()

    print("-" * 50)
    print("Batch processing finished for this run.")
    # --- 新增功能: 结束时提示剩余任务 ---
    if args.limit > 0 and len(pdbs_to_process_full) > args.limit:
        remaining_count = len(pdbs_to_process_full) - args.limit
        print(f"Successfully processed {args.limit} tasks. There are still {remaining_count} tasks left.")
        print("You can run the script again to continue processing.")

if __name__ == '__main__':
    main()