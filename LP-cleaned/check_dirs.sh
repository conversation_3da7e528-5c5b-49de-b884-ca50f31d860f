#!/bin/bash

# 脚本功能：检查一个文本文件中的每一行，看在指定目录中是否存在对应的同名子目录。
# 如果存在，则将该行内容输出到新的文件中。

# --- 参数检查 ---
if [ "$#" -ne 2 ]; then
    echo "错误：需要提供两个参数。"
    echo "用法: $0 <input_text_file> <dir_to_check>"
    exit 1
fi

INPUT_FILE="$1"
TARGET_DIR="$2"

# --- 文件和目录存在性检查 ---
if [ ! -f "$INPUT_FILE" ]; then
    echo "错误: 输入文件 '$INPUT_FILE' 不存在。"
    exit 1
fi

if [ ! -d "$TARGET_DIR" ]; then
    echo "错误: 目标目录 '$TARGET_DIR' 不存在。"
    exit 1
fi

# --- 设置输出文件名 ---
# 从输入文件名中移除 .txt (如果存在), 然后加上 _exist.txt
BASENAME=$(basename "$INPUT_FILE" .txt)
OUTPUT_FILE="${BASENAME}_exist.txt"


# --- 主逻辑 ---
# 清空或创建输出文件
> "$OUTPUT_FILE"

echo "正在处理, 请稍候..."

# 逐行读取输入文件
while IFS= read -r line || [[ -n "$line" ]]; do
    # 移除可能存在的 Windows 换行符 (\r)
    pdb_id=$(echo "$line" | tr -d '\r')

    # 跳过空行
    if [ -z "$pdb_id" ]; then
        continue
    fi

    # 检查同名子目录是否存在
    if [ -d "$TARGET_DIR/$pdb_id" ]; then
        # 如果存在, 将 pdb_id 追加到输出文件
        echo "$pdb_id" >> "$OUTPUT_FILE"
    fi
done < "$INPUT_FILE"

echo "处理完成！"
echo "存在对应子目录的 ID 已保存至: $OUTPUT_FILE"
