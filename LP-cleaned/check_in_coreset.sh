#!/bin/bash

# 检查 coreset_285.txt 文件中的每一行是否存在于 CL1_train_exist.txt 文件中

# 用法: ./check_in_coreset.sh

file1="CL1_train_exist.txt"
file2="coreset_285.txt"

if [ ! -f "$file1" ]; then
    echo "错误: 文件 '$file1' 不存在。"
    exit 1
fi

if [ ! -f "$file2" ]; then
    echo "错误: 文件 '$file2' 不存在。"
    exit 1
fi

echo "开始检查..."

found_count=0

# 读取 file1 的每一行
while IFS= read -r line1; do
    # 检查当前行是否存在于 file2 中
    if grep -Fxq "$line1" "$file2"; then
        echo "找到匹配: '$line1' 存在于 $file2"
        ((found_count++))
    fi
done < "$file1"

echo "检查完成。"
echo "总结:"
echo "  - 在 $file2 中找到的总行数: $found_count"