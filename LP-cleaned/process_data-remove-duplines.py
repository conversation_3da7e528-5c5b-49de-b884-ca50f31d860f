# -*- coding: utf-8 -*-

def subtract_lists(file_a, file_b, output_file):
    """
    Subtracts the lines of file_b from file_a and writes the result to output_file.
    """
    try:
        with open(file_a, 'r', encoding='utf-8') as f:
            lines_a = set(f.readlines())
        
        with open(file_b, 'r', encoding='utf-8') as f:
            lines_b = set(f.readlines())
            
        lines_only_in_a = lines_a - lines_b
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(sorted(list(lines_only_in_a)))
            
        print(f"处理完成！结果已保存到 {output_file}")
        print(f"总共写入 {len(lines_only_in_a)} 行。")

    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == '__main__':
    file_general = 'general_set_excl_coreset-19159.txt'
    file_coreset = 'general_set_excl_coreset_processed-9799.txt'
    file_output = 'general_set_excl_coreset_todo.txt'
    
    subtract_lists(file_general, file_coreset, file_output)
