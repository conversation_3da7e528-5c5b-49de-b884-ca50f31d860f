import os

def extract_ebind_data():
    # Step 1: Read the list of pdb_ids to process
    todo_pdb_ids = set()
    with open('general_set_excl_coreset_todo-9360.txt', 'r') as f:
        for line in f:
            todo_pdb_ids.add(line.strip())

    # Prepare to store the results
    results = []

    # Step 2: Process the main index file
    with open('INDEX_general_PL_data.2020.txt', 'r') as f:
        for line in f:
            # Ignore comment lines
            if line.startswith('#'):
                continue

            parts = line.split()
            if len(parts) >= 4:
                pdb_id = parts[0]
                # Step 3: Check if the pdb_id is in our todo list
                if pdb_id in todo_pdb_ids:
                    ebind = parts[3]
                    results.append(f"{pdb_id},{ebind}")

    # Step 4: Write the results to the output file
    with open('pdb_ebind.csv', 'w') as f:
        # No header, as per the request for "pdb_id, Ebind" format
        for item in results:
            f.write(item + '\n')

    print(f"处理完成！结果已保存到 pdb_ebind.csv 文件中。共找到 {len(results)} 条记录。")

if __name__ == "__main__":
    extract_ebind_data()
