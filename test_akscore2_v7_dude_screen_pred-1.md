# `test_akscore2_v7_dude_screen_pred.py` 脚本技术文档

## 1. 脚本运行逻辑分析

### 1.1 完整执行流程

脚本的执行流程可以分为以下几个主要阶段：

#### 阶段1：初始化和模型加载（第246-284行）
1. **参数解析**：解析命令行参数，包括数据路径、模型路径、网络参数等
2. **模型初始化**：创建GATv2模型实例，设置网络架构参数
3. **模型加载**：从checkpoint文件加载预训练模型权重
4. **设备配置**：将模型移动到GPU设备并设置为评估模式

#### 阶段2：数据路径构建（第286-312行）
1. **结果保存目录设置**：根据`--result_save_dir`参数或模型路径确定输出目录
2. **目标蛋白路径构建**：
   ```python
   target_dir = os.path.join(args.screen_dir, target_name)
   target_dir = os.path.join(target_dir, "glide_graph_one")
   ```
3. **数据集创建**：实例化`akscore2_dataset`类，扫描目标目录下的所有`.pkl.gz`文件

#### 阶段3：数据加载和预测（第315-331行）
1. **批量数据加载**：使用DataLoader按批次加载数据
2. **图数据处理**：对每个`.pkl.gz`文件中的图数据进行以下处理：
   - 解压缩并加载pickle数据
   - 调用`graph_modification`函数分解复合物图
   - 创建四种图表示：原始图、蛋白质图、配体图、蛋白质-配体交互图
3. **模型预测**：
   ```python
   bind_logits, rmsd_logits = model(graph_batch, protein_graph_batch, 
                                   ligand_graph_batch, protein_ligand_graph_batch)
   bind_rmsd_logits = bind_logits + rmsd_logits
   ```
4. **结果收集**：将预测分数和配体名称存储到结果字典中

#### 阶段4：结果保存（第331行）
调用`save_result`函数将预测结果保存到文件

### 1.2 数据处理管道详解

#### 图数据分解过程（`graph_modification`函数，第136-160行）
1. **边类型识别**：根据边特征的前3个元素识别边类型：
   - `[1,0,0]`：蛋白质内部边
   - `[0,1,0]`：配体内部边  
   - `[0,0,1]`：蛋白质-配体交互边

2. **节点分离**：通过配体边的最小索引确定蛋白质和配体节点的分界点

3. **子图构建**：
   - **蛋白质图**：仅包含蛋白质节点和蛋白质内部边
   - **配体图**：仅包含配体节点和配体内部边（重新索引）
   - **交互图**：包含所有节点但仅保留蛋白质-配体交互边

### 1.3 模型预测过程

#### GATv2模型架构（第39-107行）
1. **节点特征转换**：将输入节点特征映射到隐藏维度
2. **边距离过滤**：根据`cut_edge_dis`参数过滤远距离边：
   - `cut_edge_dis=6`：保留6Å内的边
   - `cut_edge_dis=4`：保留4Å内的边
   - `cut_edge_dis=8`：保留所有边（默认）
3. **图注意力网络**：通过5层GATv2Conv进行特征学习
4. **全局池化**：使用`global_mean_pool`聚合节点特征
5. **双头预测**：
   - `bind_logits`：结合亲和力预测
   - `rmsd_logits`：RMSD预测
   - 最终分数：`bind_rmsd_logits = bind_logits + rmsd_logits`

## 2. 输入文件架构详解

### 2.1 目录结构要求

脚本要求严格的目录结构：

```
{screen_dir}/
├── {target_name}/
│   └── glide_graph_one/
│       ├── compound_1.pkl.gz
│       ├── compound_2.pkl.gz
│       ├── compound_3.pkl.gz
│       └── ...
└── other_targets/
    └── glide_graph_one/
        └── ...
```

#### 路径构建逻辑：
1. **基础路径**：`target_dir = os.path.join(args.screen_dir, target_name)`
2. **数据路径**：`target_dir = os.path.join(target_dir, "glide_graph_one")`

#### 示例路径：
- `--screen_dir="/data/DUD_E"`
- `--target_name="aa2ar"`
- 最终数据路径：`/data/DUD_E/aa2ar/glide_graph_one/`

### 2.2 pkl.gz文件内部结构

#### 文件格式
每个`.pkl.gz`文件是gzip压缩的Python pickle文件，包含一个图对象列表：

```python
# 文件加载过程（第174-176行）
with gzip.open(graph_path, 'rb') as f:
    pdb_id_graphs = pickle.load(f)  # 返回图对象列表
```

#### 数据内容
`pdb_id_graphs`是一个列表，每个元素是PyTorch Geometric的`Data`对象，包含：

1. **节点特征 (x)**：形状为`[num_nodes, 73]`的张量
   - 表示蛋白质和配体原子的特征
   - 包含原子类型、电荷、坐标等信息

2. **边索引 (edge_index)**：形状为`[2, num_edges]`的张量
   - 定义原子间的连接关系
   - 包含共价键和非共价相互作用

3. **边特征 (edge_attr)**：形状为`[num_edges, 24]`的张量
   - 前3个元素：边类型标识（one-hot编码）
   - 后续元素：距离、角度等几何特征

4. **图名称 (name)**：在第186行被重新赋值为文件名（去除.pkl.gz后缀）

### 2.3 图数据格式详解

#### 边类型编码（第139-141行）
```python
protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]  
protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]
```

#### 节点组织方式
- **蛋白质节点**：索引从0开始到`protein_ligand_node_sep_idx-1`
- **配体节点**：索引从`protein_ligand_node_sep_idx`开始到末尾
- **分界点确定**：`protein_ligand_node_sep_idx = torch.min(ligand_edge_index)`

## 3. 输出文件架构和内容

### 3.1 输出目录结构

#### 目录构建逻辑（第235-239行）
```python
result_save_dir = os.path.join(result_save_dir, "glide_sp")
result_save_dir_1 = result_save_dir + "_bind_rmsd"
if not os.path.exists(result_save_dir_1):
    os.makedirs(result_save_dir_1)
result_save_path = os.path.join(result_save_dir_1, target_name + '.txt')
```

#### 示例输出路径
- 输入：`--result_save_dir="/results"`, `--target_name="aa2ar"`
- 输出文件：`/results/glide_sp_bind_rmsd/aa2ar.txt`

### 3.2 文件命名规则

输出文件名格式：`{target_name}.txt`

### 3.3 文件内容格式

#### 输出格式（第240-244行）
```python
for i, _ in enumerate(result['names']):
    name = result['names'][i]
    name = name.split("_")[0] +"_" +name.split("_")[-1]  # 名称处理
    f.write(f"{name}\t{i}\t{result['bind_rmsd_preds'][i]:.4f}\n")
```

#### 文件结构
每行包含三列，用制表符分隔：
1. **配体名称**：处理后的配体标识符
2. **索引**：配体在结果中的序号（从0开始）
3. **预测分数**：bind_rmsd_preds值，保留4位小数

#### 示例输出内容
```
compound_1    0    -2.3456
compound_2    1    -1.8923
compound_3    2    -3.1234
...
```

### 3.4 结果后处理

#### 名称处理逻辑
```python
name = name.split("_")[0] +"_" +name.split("_")[-1]
```
- 提取原始名称的第一部分和最后一部分
- 用下划线连接
- 例：`compound_pose_1_final` → `compound_final`

#### 分数计算
最终预测分数是结合亲和力预测和RMSD预测的简单相加：
```python
bind_rmsd_logits = bind_logits + rmsd_logits
```

## 4. 关键参数影响分析

### 4.1 路径相关参数

#### `--screen_dir`
- **作用**：指定包含所有目标蛋白数据的根目录
- **影响**：决定数据搜索的起始路径
- **要求**：必须包含以目标名称命名的子目录

#### `--target_name`  
- **作用**：指定要处理的目标蛋白名称
- **影响**：确定具体的数据子目录路径
- **路径构建**：`{screen_dir}/{target_name}/glide_graph_one/`

#### `--result_save_dir`
- **作用**：指定结果保存的根目录
- **默认行为**：如果未指定，使用模型路径（去除.pth后缀）
- **最终路径**：`{result_save_dir}/glide_sp_bind_rmsd/{target_name}.txt`

#### `--model_path`
- **作用**：指定预训练模型文件路径
- **要求**：必须是有效的PyTorch checkpoint文件
- **影响**：决定模型权重和架构参数

### 4.2 网络架构参数

#### `--cut_edge_dis`（默认：8）
- **作用**：控制边距离过滤阈值
- **选项**：4Å、6Å、8Å
- **影响**：较小值会减少计算量但可能丢失长程相互作用信息

#### `--hidden_dim`（默认：256）
- **作用**：设置GNN隐藏层维度
- **影响**：影响模型容量和计算复杂度

#### `--num_layers`（默认：5）
- **作用**：设置GNN层数
- **影响**：控制模型深度和感受野大小

### 4.3 数据处理参数

#### `--batch_size`（默认：4）
- **作用**：控制批处理大小
- **影响**：影响内存使用和处理速度
- **建议**：根据GPU内存调整

#### `--num_workers`（默认：4）
- **作用**：设置数据加载的并行进程数
- **影响**：影响数据加载速度

这个脚本实现了一个完整的蛋白质-配体结合预测流程，从图数据加载到模型预测再到结果保存，为虚拟筛选提供了强大的工具。
