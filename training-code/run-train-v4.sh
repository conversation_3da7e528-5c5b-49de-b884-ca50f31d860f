export CUDA_VISIBLE_DEVICES=1

eval "$(~/.miniconda3-1/bin/conda shell.bash hook)"
conda activate pytorch-1

python train_akscore2_v4.py \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --num_workers 8 \
    --batch_size 32 \
    --model_save_dir ./v4_model_save_dir \
    --data_dir ~/my_private_on_AT1/ak-score2-lp-data/ak-score2-data \
    --train_list_path ~/my_private_on_AT1/ak-score2-lp-data/CL1_train_exist.txt \
    --valid_list_path ~/my_private_on_AT1/ak-score2-lp-data/CL2_val_exist.txt \
    --lr 0.0001

    