# train_akscore2_v7_bind_rmsd.py 中 suc_top1 和 suc_top5 指标详解

## 概述

本文档详细解释了 `training-code/train_akscore2_v7_bind_rmsd.py` 脚本中日志输出文件 `train_val_log.tsv` 里的 `suc_top1` 和 `suc_top5` 这两个重要评估指标。

## 1. 指标含义

在蛋白质-配体对接评估中：

- **`suc_top1`**: Top-1成功率
  - 表示天然构象（native pose）在模型预测排名中位于前3名的比例
  - 反映模型识别正确结合姿态的精确度

- **`suc_top5`**: Top-5成功率  
  - 表示天然构象在模型预测排名中位于前10名的比例
  - 反映模型的容错能力和候选构象质量

## 2. 计算逻辑详解

### 2.1 核心计算代码

在 `valid_model_epoch` 函数中的关键实现：

```python
preds = bind_logits + rmsd_logits
native_index = data_type_dict_batch["native"][0]
#### success rate in top n
top_list = torch.topk(preds, 10, dim=0, largest=False).indices.squeeze().tolist()
target_count = target_count + 1
if native_index in top_list:
    success_count["top5"] += 1
if native_index in top_list[:3]:
    success_count["top1"] += 1
```

### 2.2 计算步骤详解

1. **综合评分计算**
   - `preds = bind_logits + rmsd_logits`：将结合亲和力预测值和RMSD预测值相加作为综合评分
   - 这个设计理念是：好的构象应该同时具有强的结合亲和力（低bind_logits）和低的RMSD（低rmsd_logits）

2. **天然构象识别**
   - `native_index = data_type_dict_batch["native"][0]`：获取天然构象在当前批次中的索引
   - 天然构象通过文件名中的标识符 `_0_` 来识别

3. **排序和排名**
   - `torch.topk(preds, 10, dim=0, largest=False)`：按综合评分从低到高排序，取前10名
   - `largest=False` 表示取最小值，因为更低的评分代表更好的构象

4. **成功率统计**
   - **Top-1成功**: 检查天然构象是否在前3名中（`top_list[:3]`）
   - **Top-5成功**: 检查天然构象是否在前10名中（`top_list`）

5. **最终成功率计算**
   ```python
   epoch_loss["suc_top1"] = success_count["top1"] / target_count
   epoch_loss["suc_top5"] = success_count["top5"] / target_count
   ```

## 3. 虚拟筛选性能评估意义

### 3.1 实际应用价值

- **实用性评估**: Top-1成功率反映模型在实际应用中快速识别正确构象的能力
- **容错性评估**: Top-5成功率评估模型的鲁棒性，即使最佳预测不完全准确，正确答案仍在候选列表中
- **筛选效率**: 高的Top-N成功率意味着研究人员需要验证的候选构象更少，显著提高药物发现效率

### 3.2 与其他指标的关系

- **与损失函数的互补**: 损失函数关注预测精度，而Top-N成功率关注实际排序性能
- **与RMSD/结合亲和力的区别**: 单独的RMSD或结合亲和力预测可能准确，但综合排序能力更重要
- **实际意义**: 在药物发现中，研究人员通常只会选择排名最高的几个候选进行昂贵的实验验证

### 3.3 评估标准

- **优秀性能**: Top-1 > 60%, Top-5 > 80%
- **可接受性能**: Top-1 > 40%, Top-5 > 60%
- **需要改进**: Top-1 < 30%, Top-5 < 50%

## 4. 重要注意事项

### 4.1 命名不一致问题

需要注意的是，当前代码中存在命名不一致的问题：
- `suc_top1` 实际检查的是前3名（而非前1名）
- `suc_top5` 实际检查的是前10名（而非前5名）

这种实现可能会导致对模型性能的误解，建议在解读结果时要明确实际的计算逻辑。

### 4.2 与v4版本的区别

相比于 `train_akscore2_v4.py` 中的类似指标：
- v7版本使用综合评分（结合亲和力 + RMSD）进行排序
- v4版本仅使用RMSD预测值进行排序
- v7版本的多任务学习框架使得排序更加全面和准确

## 5. 总结

`suc_top1` 和 `suc_top5` 指标通过评估天然构象在综合评分排序中的位置，直接反映了模型在虚拟筛选任务中的实用性能。它们是评估蛋白质-配体对接模型实际应用价值的关键指标，比单纯的回归损失更能体现模型在真实药物发现场景中的表现。

这种评估方式确保了训练出的模型不仅在统计指标上表现良好，更重要的是在实际虚拟筛选应用中具有实用价值，能够有效地从大量候选构象中识别出最有可能的正确结合姿态。