# train_akscore2_v7_bind_rmsd.py 验证过程中的构象排序环境详解

## 概述

本文档详细说明了 `training-code/train_akscore2_v7_bind_rmsd.py` 脚本在验证过程中，天然构象（native pose）是在哪些类型和数量的构象中进行排序的，以及这些构象是如何组合形成排序环境的。

## 1. 验证批次的构象组成

### 1.1 每个验证批次包含的构象数量

每个验证批次包含的构象数量是**变量的**，取决于每个PDB ID下实际可用的构象数量。与训练模式的固定9个构象不同，验证模式加载指定PDB ID的所有可用构象。

### 1.2 核心数据加载逻辑

```python
if self.train_mode:
    # 训练模式：固定采样策略（9个构象）
    iter_graph_paths.extend(self.path_dict_list[idx]["native"])
    for random_i in random.sample(range(0, self.len_dataset), 4):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["dock"], 1))
    for random_i in random.sample(range(0, self.len_dataset), 2):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["cross"], 1))
    for random_i in random.sample(range(0, self.len_dataset), 2):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["random"], 1))
else:
    # 验证模式：加载指定PDB ID的所有构象
    for graph_path in self.path_dict_list[idx].values():
        iter_graph_paths.extend(graph_path)
```

## 2. 构象类型和数量分布

### 2.1 构象类型识别

构象类型通过文件名中的标识符进行识别：

```python
dump_paths = {
    "native": [],
    "dock": [],
    "cross": [],
    "random": [],
}
for pdb_id_filename in pdb_id_filenames:
    name_splited = pdb_id_filename.split('_')
    if name_splited[-2] == '0':
        dump_paths["native"].append(pdb_id_file_path)
    elif name_splited[-2] == '1':
        dump_paths["dock"].append(pdb_id_file_path)
    elif name_splited[-2] == '2':
        dump_paths["cross"].append(pdb_id_file_path)
    elif name_splited[-2] == '3':
        dump_paths["random"].append(pdb_id_file_path)
```

### 2.2 各类型构象详解

在验证模式下，每个批次包含：

- **天然构象 (Native, `_0_`)**: **1个**
  - 真实的晶体结构构象，作为"金标准"
  - 通过文件名中的 `_0_` 标识符识别
  - 这是排序评估的目标构象

- **对接构象 (Docked, `_1_`)**: **变量数量**
  - 通过分子对接算法（如AutoDock Vina）生成的构象
  - 作为"高质量负样本"或"难例"
  - 通常具有较好的结合姿态，增加识别难度

- **交叉构象 (Cross, `_2_`)**: **变量数量**
  - 来自其他蛋白质靶点的配体构象
  - 作为"中等质量负样本"
  - 模拟虚拟筛选中的非特异性结合

- **随机构象 (Random, `_3_`)**: **变量数量**
  - 随机生成的配体构象
  - 作为"低质量负样本"或"易例"
  - 提供基础的对比基准

## 3. 批次构建和排序环境形成

### 3.1 数据类型识别函数

```python
def get_data_type(name_list):
    data_type_dict_batch = {
        "native": [],
        "dock": [],
        "cross": [],
        "random": [],
    }
    name_list = list(chain(*name_list))
    for name_i, name in enumerate(name_list):
        name_splited = name.split('_')
        if name_splited[-2] == '0':
            data_type_dict_batch["native"].append(name_i)
        elif name_splited[-2] == '1':
            data_type_dict_batch["dock"].append(name_i)
        elif name_splited[-2] == '2':
            data_type_dict_batch["cross"].append(name_i)
        elif name_splited[-2] == '3':
            data_type_dict_batch["random"].append(name_i)
    return data_type_dict_batch
```

### 3.2 验证过程中的排序逻辑

```python
# 获取天然构象的索引
native_index = data_type_dict_batch["native"][0]

# 计算综合评分并排序
preds = bind_logits + rmsd_logits
top_list = torch.topk(preds, 10, dim=0, largest=False).indices.squeeze().tolist()

# 评估成功率
target_count = target_count + 1
if native_index in top_list:
    success_count["top5"] += 1
if native_index in top_list[:3]:
    success_count["top1"] += 1
```

## 4. 训练模式 vs 验证模式对比

### 4.1 训练模式的固定采样策略

训练时每个批次**固定包含9个构象**：
- 1个天然构象（来自当前PDB ID）
- 4个对接构象（从不同PDB ID随机采样）
- 2个交叉构象（从不同PDB ID随机采样）
- 2个随机构象（从不同PDB ID随机采样）

**特点**：
- 固定的批次大小
- 跨PDB ID的随机采样
- 增加训练数据的多样性

### 4.2 验证模式的完整评估策略

验证时每个批次包含**变量数量的构象**：
- 1个天然构象（来自当前PDB ID）
- 所有可用的对接构象（来自当前PDB ID）
- 所有可用的交叉构象（来自当前PDB ID）
- 所有可用的随机构象（来自当前PDB ID）

**特点**：
- 变量的批次大小
- 同一PDB ID内的完整评估
- 真实反映每个靶点的构象分布

## 5. 排序环境的实际特征

### 5.1 挑战性和复杂性

- **多层次难度**: 包含从高质量对接构象到低质量随机构象的完整难度梯度
- **真实场景模拟**: 模拟实际虚拟筛选中需要从大量候选构象中识别正确结合姿态的场景
- **干扰项丰富**: 多种类型的负样本增加了识别天然构象的难度

### 5.2 评估公平性和准确性

- **同源比较**: 所有构象都来自同一个PDB ID，确保蛋白质环境一致
- **完整评估**: 不进行采样，使用该PDB ID下的所有可用构象
- **无偏估计**: 避免了采样偏差，提供更准确的性能评估

### 5.3 统计意义

- **样本量充足**: 通常每个PDB ID包含数十到上百个构象
- **分布真实**: 保持了原始数据集中各类型构象的自然分布
- **可重现性**: 验证结果具有良好的可重现性

## 6. 实际应用意义

### 6.1 虚拟筛选性能评估

这种排序环境设计直接对应于实际虚拟筛选的应用场景：
- **候选构象筛选**: 从大量候选中识别最有可能的结合姿态
- **排序质量评估**: 评估模型对不同质量构象的区分能力
- **实用性验证**: 确保模型在真实应用中的有效性

### 6.2 模型性能指标的意义

- **Top-N成功率**: 直接反映模型在实际应用中的表现
- **鲁棒性评估**: 测试模型在复杂环境中的稳定性
- **泛化能力**: 评估模型对不同类型构象的处理能力

## 7. 总结

在 `train_akscore2_v7_bind_rmsd.py` 的验证过程中，天然构象需要在一个包含多种类型构象的复杂且真实的环境中进行排序。这个环境通常包含：

- **1个天然构象**（评估目标）
- **若干个对接构象**（高质量干扰项）
- **若干个交叉构象**（中等质量干扰项）
- **若干个随机构象**（低质量干扰项）

总构象数量取决于每个PDB ID下实际可用的构象数量，通常在几十到上百个之间。这种设计确保了模型评估的全面性、真实性和公平性，能够准确反映模型在实际虚拟筛选任务中的性能表现，为药物发现提供可靠的计算工具评估标准。