#export CUDA_VISIBLE_DEVICES=0

eval "$(~/.miniconda3-1/bin/conda shell.bash hook)"
conda activate pytorch-1

python train_akscore2_v8.py \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --cut_edge_dis 8 \
    --num_workers 8 \
    --batch_size 16 \
    --model_save_dir ./v8_model_save_dir \
    --data_dir ~/my_private_on_AT1/ak-score2-lp-data/ak-score2-data \
    --train_list_path ~/my_private_on_AT1/ak-score2-lp-data/CL1_train_exist.txt \
    --valid_list_path ~/my_private_on_AT1/ak-score2-lp-data/CL2_val_exist.txt \
    --lr 0.0001

# batch_size 8 for deep GPU 12GB 
# batch_size 16 for deep GPU 24GB
    