# train_akscore2_v8.py 中 suc_top1 和 suc_top5 指标详解

## 概述

本文档详细解释了 `training-code/train_akscore2_v8.py` 脚本中日志输出文件 `train_val_log.tsv` 里的 `suc_top1` 和 `suc_top5` 这两个重要评估指标。

## 1. 指标含义

在蛋白质-配体对接评估中：

- **`suc_top1`**: Top-1成功率
  - 表示天然构象（native pose）在模型预测排名中位于**前3名**的比例
  - 反映模型识别正确结合姿态的精确度

- **`suc_top5`**: Top-5成功率  
  - 表示天然构象在模型预测排名中位于**前10名**的比例
  - 反映模型的容错能力和候选构象质量

⚠️ **重要注意**: 代码中存在命名不一致问题 - `suc_top1` 实际检查前3名，`suc_top5` 实际检查前10名。

## 2. 计算逻辑详解

### 2.1 核心计算代码

在 `valid_model_epoch` 函数中的关键实现：

```python
native_index = data_type_dict_batch["native"][0]
#### success rate in top n
top_list = torch.topk(bind_logits, 10, dim=0, largest=False).indices.squeeze().tolist()
target_count = target_count + 1
if native_index in top_list:
    success_count["top5"] += 1
if native_index in top_list[:3]:
    success_count["top1"] += 1
```

### 2.2 计算步骤详解

1. **获取天然构象索引**
   - `native_index = data_type_dict_batch["native"][0]`：获取天然构象在当前批次中的索引
   - 天然构象通过文件名中的标识符 `_0_` 来识别

2. **模型预测排序**
   - `torch.topk(bind_logits, 10, dim=0, largest=False)`：按结合亲和力预测值从低到高排序，取前10名
   - `largest=False` 表示取最小值，因为更低的结合亲和力分数代表更好的构象

3. **成功率统计**
   - **Top-1成功**: 检查天然构象是否在前3名中（`top_list[:3]`）
   - **Top-5成功**: 检查天然构象是否在前10名中（`top_list`）

4. **最终成功率计算**
   ```python
   epoch_loss["suc_top1"] = success_count["top1"] / target_count
   epoch_loss["suc_top5"] = success_count["top5"] / target_count
   ```

### 2.3 数据批次构成

每个验证批次包含9个构象：
- 1个天然构象（native pose）
- 4个对接构象（docked poses）
- 2个交叉构象（cross decoys）
- 2个随机构象（random decoys）

模型需要从这9个候选中识别出天然构象并给予最佳评分。

## 3. 与其他版本的对比

### 3.1 与v7版本的区别

**v7版本（多任务学习）**：
```python
preds = bind_logits + rmsd_logits  # 综合评分
top_list = torch.topk(preds, 10, dim=0, largest=False).indices.squeeze().tolist()
```

**v8版本（单任务学习）**：
```python
top_list = torch.topk(bind_logits, 10, dim=0, largest=False).indices.squeeze().tolist()
```

v8版本仅使用结合亲和力预测值进行排序，这使得评估更专注于结合亲和力预测的准确性。

### 3.2 与v4版本的区别

**v4版本（二分类模型）**：
```python
top_list = torch.topk(rmsd_logits_sigmoid, 5, dim=0, largest=True).indices.squeeze().tolist()
if native_index in top_list[:5]:
    success_count["top5"] += 1
if top_list[0] == native_index:
    success_count["top1"] += 1
```

v4版本使用RMSD预测和sigmoid激活，检查的是真正的Top-1和Top-5。

### 3.3 版本对比总结

| 版本 | 排序依据 | Top-1实际检查 | Top-5实际检查 | 学习范式 |
|------|----------|---------------|---------------|----------|
| v4 | RMSD预测值 | 前1名 | 前5名 | 二分类 |
| v7 | 结合能+RMSD | 前3名 | 前10名 | 多任务回归 |
| v8 | 结合能预测值 | 前3名 | 前10名 | 单任务回归 |

## 4. v8版本的核心创新：标签增强技术

### 4.1 标签增强原理

v8版本的关键创新在于将RMSD信息编码到结合亲和力的训练标签中：

```python
def graph_rmsd_bindaff_aug(self, graph_data, rmsd_cutoff, bind_aff_aug, bind_aff_rej):
    """
    根据RMSD值对结合亲和力标签进行增强的关键函数
    """
    if graph_data.rmsd <= rmsd_cutoff:  # 好构象
        # 在指定范围内随机增强结合亲和力
        graph_data.bind_aff += random.uniform(bind_aff_aug[0], bind_aff_aug[1])
    else:  # 差构象
        # 在指定范围内随机降低结合亲和力
        graph_data.bind_aff += random.uniform(bind_aff_rej[0], bind_aff_rej[1])
```

### 4.2 标签增强的意义

1. **隐式多任务学习**: 虽然只有一个输出头，但通过标签增强实现了对姿势质量的感知
2. **简化模型架构**: 避免了v7版本的双输出头设计，降低了模型复杂度
3. **提高排序性能**: 使模型能够根据构象质量给出差异化的结合亲和力预测

## 5. 虚拟筛选性能评估意义

### 5.1 实际应用价值

- **实用性评估**: Top-1成功率反映模型在实际应用中快速识别正确构象的能力
- **容错性评估**: Top-5成功率评估模型的鲁棒性，即使最佳预测不完全准确，正确答案仍在候选列表中
- **筛选效率**: 高的Top-N成功率意味着研究人员需要验证的候选构象更少，显著提高药物发现效率

### 5.2 评估标准

- **优秀性能**: Top-1 > 60%, Top-5 > 80%
- **可接受性能**: Top-1 > 40%, Top-5 > 60%  
- **需要改进**: Top-1 < 30%, Top-5 < 50%

### 5.3 与损失函数的关系

这些指标与训练时的复合损失函数相呼应：

```python
total_loss = loss_bind_native_mse + loss_bind_cross_random + loss_bind_dock_mse
```

- `loss_bind_native_mse`: 优化天然构象的结合亲和力预测精度
- `loss_bind_cross_random`: 惩罚非相关构象，提高排序质量  
- `loss_bind_dock_mse`: 通过RMSD增强的标签优化对接构象评估

## 6. 重要注意事项

### 6.1 命名不一致问题

当前代码中存在命名不一致的问题：
- `suc_top1` 实际检查的是前3名（而非前1名）
- `suc_top5` 实际检查的是前10名（而非前5名）

这种实现可能会导致对模型性能的误解，建议在解读结果时要明确实际的计算逻辑。

### 6.2 与传统指标的互补性

- **与损失函数的互补**: 损失函数关注预测精度，而Top-N成功率关注实际排序性能
- **与RMSD/结合亲和力的区别**: 单独的RMSD或结合亲和力预测可能准确，但综合排序能力更重要
- **实际意义**: 在药物发现中，研究人员通常只会选择排名最高的几个候选进行昂贵的实验验证

## 7. 验证过程中的构象排序环境详解

### 7.1 验证批次的构象组成

#### 7.1.1 每个验证批次包含的构象数量

每个验证批次包含的构象数量是**变量的**，取决于每个PDB ID下实际可用的构象数量，通常在30-200个构象之间。

#### 7.1.2 核心数据加载逻辑

```python
def get(self, idx):
    pdb_id_graphs = []
    iter_graph_paths = []
    if self.train_mode:
        # 训练模式逻辑
        iter_graph_paths.extend(self.path_dict_list[idx]["native"])
        for random_i in random.sample(range(0, self.len_dataset), 4):
            iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["dock"], 1))
        for random_i in random.sample(range(0, self.len_dataset), 2):
            iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["cross"], 1))
        for random_i in random.sample(range(0, self.len_dataset), 2):
            iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["random"], 1))
    else:
        # 验证模式：加载指定PDB ID的所有构象
        iter_graph_paths.extend(self.path_dict_list[idx]["native"])
        iter_graph_paths.extend(self.path_dict_list[idx]["dock"])
        iter_graph_paths.extend(self.path_dict_list[idx]["cross"])
        iter_graph_paths.extend(self.path_dict_list[idx]["random"])
```

### 7.2 构象类型和数量分布

#### 7.2.1 构象类型识别

构象类型通过文件名中的标识符进行识别：

```python
dump_paths = {
    "native": [],
    "dock": [],
    "cross": [],
    "random": [],
}
for pdb_id_filename in pdb_id_filenames:
    name_splited = pdb_id_filename.split('_')
    if name_splited[-2] == '0':
        dump_paths["native"].append(pdb_id_file_path)
    elif name_splited[-2] == '1':
        dump_paths["dock"].append(pdb_id_file_path)
    elif name_splited[-2] == '2':
        dump_paths["cross"].append(pdb_id_file_path)
    elif name_splited[-2] == '3':
        dump_paths["random"].append(pdb_id_file_path)
```

#### 7.2.2 各类型构象详解

在验证模式下，每个批次包含：

- **天然构象 (Native, `_0_`)**: **变量数量**
  - 真实的晶体结构构象，作为"金标准"
  - 通常每个PDB ID有1个天然构象
  - 这是排序评估的目标构象

- **对接构象 (Docked, `_1_`)**: **变量数量**
  - 通过分子对接算法生成的构象
  - 作为"高质量负样本"或"难例"
  - 数量取决于数据集中该PDB ID的对接构象数量

- **交叉构象 (Cross, `_2_`)**: **变量数量**
  - 来自其他蛋白质靶点的配体构象
  - 作为"中等质量负样本"
  - 数量取决于数据集中该PDB ID的交叉构象数量

- **随机构象 (Random, `_3_`)**: **变量数量**
  - 随机生成的配体构象
  - 作为"低质量负样本"或"易例"
  - 数量取决于数据集中该PDB ID的随机构象数量

### 7.3 训练模式 vs 验证模式的关键区别

#### 7.3.1 训练模式的采样策略

```python
if self.train_mode:
    iter_graph_paths.extend(self.path_dict_list[idx]["native"])  # 1个native
    # 从不同PDB ID随机采样4个dock构象
    for random_i in random.sample(range(0, self.len_dataset), 4):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["dock"], 1))
    # 从不同PDB ID随机采样2个cross构象
    for random_i in random.sample(range(0, self.len_dataset), 2):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["cross"], 1))
    # 从不同PDB ID随机采样2个random构象
    for random_i in random.sample(range(0, self.len_dataset), 2):
        iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["random"], 1))
```

**训练模式特点**：
- **固定比例**: 1个native + 4个dock + 2个cross + 2个random = 9个构象
- **跨PDB采样**: dock、cross、random构象来自不同的PDB ID
- **随机性**: 每个epoch都会重新随机采样

#### 7.3.2 验证模式的加载策略

```python
else:
    # 验证模式：加载指定PDB ID的所有构象
    iter_graph_paths.extend(self.path_dict_list[idx]["native"])
    iter_graph_paths.extend(self.path_dict_list[idx]["dock"])
    iter_graph_paths.extend(self.path_dict_list[idx]["cross"])
    iter_graph_paths.extend(self.path_dict_list[idx]["random"])
```

**验证模式特点**：
- **同源性**: 所有构象都来自同一个PDB ID
- **完整性**: 加载该PDB ID下的所有可用构象
- **真实性**: 反映真实虚拟筛选场景中的构象分布

### 7.4 批次构建和排序环境形成

#### 7.4.1 数据类型识别函数

```python
def get_data_type(name_list):
    data_type_dict_batch = {
        "native": [],
        "dock": [],
        "cross": [],
        "random": [],
    }
    name_list = list(chain(*name_list))
    for name_i, name in enumerate(name_list):
        name_splited = name.split('_')
        if name_splited[-2] == '0':
            data_type_dict_batch["native"].append(name_i)
        elif name_splited[-2] == '1':
            data_type_dict_batch["dock"].append(name_i)
        elif name_splited[-2] == '2':
            data_type_dict_batch["cross"].append(name_i)
        elif name_splited[-2] == '3':
            data_type_dict_batch["random"].append(name_i)
    return data_type_dict_batch
```

#### 7.4.2 验证过程中的排序逻辑

```python
def valid_model_epoch(model, loader, criterion):
    for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):
        data_type_dict_batch = get_data_type(graph_batch.name)
        
        bind_logits = model(graph_batch.to(DEVICE), ...)
        
        native_index = data_type_dict_batch["native"][0]
        top_list = torch.topk(bind_logits, 10, dim=0, largest=False).indices.squeeze().tolist()
        
        if native_index in top_list:
            success_count["top5"] += 1
        if native_index in top_list[:3]:
            success_count["top1"] += 1
```

### 7.5 验证模式的实际构象数量

#### 7.5.1 理论上的构象数量

根据代码逻辑，验证模式下每个批次的构象数量是**变量的**，取决于每个PDB ID下实际可用的构象数量。

#### 7.5.2 实际数据分布（基于常见数据集）

在典型的蛋白质-配体对接数据集中，每个PDB ID通常包含：
- **Native构象**: 1个
- **Dock构象**: 10-100个不等
- **Cross构象**: 10-50个不等  
- **Random构象**: 10-50个不等

**总计**: 通常每个验证批次包含30-200个构象不等。

### 7.6 与其他版本的对比

#### 7.6.1 与v7版本的相似性

v8版本的验证模式与v7版本基本相同，都是加载指定PDB ID的所有构象：

```python
else:
    iter_graph_paths.extend(self.path_dict_list[idx]["native"])
    iter_graph_paths.extend(self.path_dict_list[idx]["dock"])
    iter_graph_paths.extend(self.path_dict_list[idx]["cross"])
    iter_graph_paths.extend(self.path_dict_list[idx]["random"])
```

#### 7.6.2 与v4版本的区别

v4版本在验证模式下排除了dock构象：

```python
elif name_splited[-2] == '1':
    self.decoy_dock_paths.append(pdb_id_file_path)
    # 注意：这行被注释了，对接构象不参与验证
    # valid_dump_list.append(pdb_id_file_path)
```

### 7.7 验证环境的优势

#### 7.7.1 真实性

- **同源构象**: 所有构象来自同一个蛋白质-配体系统
- **自然分布**: 保持了原始数据集中各类型构象的自然比例
- **实际场景**: 更接近真实虚拟筛选中的候选构象分布

#### 7.7.2 全面性

- **多样性**: 包含了从高质量到低质量的各种构象类型
- **挑战性**: dock构象作为高质量干扰项，增加了识别难度
- **层次性**: 不同类型构象形成了质量梯度，便于评估模型的区分能力

### 7.8 验证排序环境总结

在 `train_akscore2_v8.py` 的验证过程中，天然构象需要在一个包含**变量数量**构象的复杂环境中进行排序。这个环境包含：

- **1个天然构象**（评估目标）
- **若干个对接构象**（高质量干扰项）
- **若干个交叉构象**（中等质量干扰项）
- **若干个随机构象**（低质量干扰项）

总构象数量通常在30-200个之间，具体取决于数据集中每个PDB ID的构象数量。这种设计确保了模型评估的真实性和全面性，能够准确反映模型在实际虚拟筛选任务中的性能表现。

## 8. 总结

`suc_top1` 和 `suc_top5` 指标通过评估天然构象在结合亲和力预测排序中的位置，直接反映了模型在虚拟筛选任务中的实用性能。它们是评估蛋白质-配体对接模型实际应用价值的关键指标，比单纯的回归损失更能体现模型在真实药物发现场景中的表现。

v8版本通过巧妙的标签增强技术，在保持单任务学习简洁性的同时，实现了对构象质量的有效感知，使得这些成功率指标能够准确反映模型的综合性能。

这种评估方式确保了训练出的模型不仅在统计指标上表现良好，更重要的是在实际虚拟筛选应用中具有实用价值，能够有效地从大量候选构象中识别出最有可能的正确结合姿态。
