# train_akscore2_v4.py 中 valid_suc_top1 和 valid_suc_top5 指标详解

## 概述

本文档详细解释了 `training-code/train_akscore2_v4.py` 脚本中日志输出文件 `train_val_log.csv` 里的 `valid_suc_top1` 和 `valid_suc_top5` 这两个重要评估指标。

## 1. 指标含义

在蛋白质-配体对接评估中：

- **`valid_suc_top1`**: Top-1成功率
  - 表示模型预测的最佳构象（排名第1）是天然构象的比例
  - 反映模型直接给出正确答案的能力

- **`valid_suc_top5`**: Top-5成功率
  - 表示天然构象出现在模型预测的前5名构象中的比例
  - 反映模型的容错能力和候选构象质量

这些指标衡量模型在虚拟筛选任务中识别正确结合姿态的能力。

## 2. 计算逻辑详解

### 2.1 核心计算代码

在 `valid_model_epoch` 函数中的关键实现：

```python
# 找到天然构象的索引
native_index = [i_n for i_n, name in enumerate(graph_batch.name[0]) if '_0_0' in name][0]

# 计算top-N成功率
if len(rmsd_logits_sigmoid) > 50:
    top_list = torch.topk(rmsd_logits_sigmoid, 5, dim=0, largest=True).indices.squeeze().tolist()
    target_count = target_count + 1
    if native_index in top_list[:5]:
        success_count["top5"] += 1
    if top_list[0] == native_index:
        success_count["top1"] += 1
```

### 2.2 计算步骤详解

1. **识别天然构象**
   - 通过文件名中的 `_0_0` 标识符找到天然构象在当前批次中的索引位置
   - 天然构象是已知的正确结合姿态，作为评估的"金标准"

2. **模型预测排序**
   - 对模型输出的RMSD预测值应用sigmoid激活函数，得到0-1之间的概率分数
   - 使用 `torch.topk()` 按预测分数从高到低排序（`largest=True`）
   - 获取前5名构象的索引列表

3. **成功率统计**
   - **Top-1成功**: 检查排名第1的构象是否为天然构象（`top_list[0] == native_index`）
   - **Top-5成功**: 检查天然构象是否出现在前5名中（`native_index in top_list[:5]`）

4. **最终成功率计算**
   ```python
   epoch_loss["suc_top1"] = success_count["top1"] / target_count
   epoch_loss["suc_top5"] = success_count["top5"] / target_count
   ```

### 2.3 重要条件限制

代码中包含一个重要的条件判断：
```python
if len(rmsd_logits_sigmoid) > 50:
```

这意味着：
- 只有当一个PDB复合物的构象数量超过50个时，才会计算成功率指标
- 确保统计的有效性，避免在样本量过小时产生误导性结果
- 模拟真实虚拟筛选场景中的大规模候选构象集合

## 3. 验证批次的构象组成详解

### 3.1 验证数据加载逻辑

在验证模式下，`akscore2_dataset` 类的数据加载策略：

```python
for pdb_id in self.pdb_id_list:
    pdb_id_dir = os.path.join(self.data_dir, pdb_id)
    pdb_id_filenames = [f for f in os.listdir(pdb_id_dir) if f.endswith('.pkl')]
    valid_dump_list = []
    for pdb_id_filename in pdb_id_filenames:
        name_splited = pdb_id_filename.split('_')
        if name_splited[-2] == '0':  # 天然构象
            self.native_paths.append(pdb_id_file_path)
            valid_dump_list.append(pdb_id_file_path)
        elif name_splited[-2] == '1':  # 对接构象
            self.decoy_dock_paths.append(pdb_id_file_path)
            # 注意：这行被注释了，对接构象不参与验证
            # valid_dump_list.append(pdb_id_file_path)
        elif name_splited[-2] == '2':  # 交叉构象
            self.decoy_cross_paths.append(pdb_id_file_path)
            valid_dump_list.append(pdb_id_file_path)
        elif name_splited[-2] == '3':  # 随机构象
            self.decoy_random_paths.append(pdb_id_file_path)
            valid_dump_list.append(pdb_id_file_path)
```

### 3.2 验证批次构象类型和数量

每个验证批次包含以下类型的构象：

- **天然构象 (Native, `_0_`)**: **1个**
  - 真实的晶体结构构象，作为"金标准"
  - 通过 `_0_0` 标识符识别

- **对接构象 (Docked, `_1_`)**: **0个**
  - 被完全排除在验证过程之外（代码中被注释）
  - 不参与排序和成功率计算

- **交叉构象 (Cross, `_2_`)**: **变量数量**
  - 来自其他蛋白质靶点的配体构象
  - 作为"硬负样本"，增加识别难度

- **随机构象 (Random, `_3_`)**: **变量数量**
  - 随机生成的配体构象
  - 作为"容易负样本"，提供基础对比

### 3.3 验证时的排序环境

**关键特点**：
- 天然构象需要在一个包含 **1个天然构象 + N个交叉构象 + M个随机构象** 的集合中脱颖而出
- 总构象数量必须 > 50 才进行成功率计算
- 排序基于模型对每个构象的RMSD预测值（经sigmoid激活）

### 3.4 验证场景的实际意义

这种验证设置模拟了真实的虚拟筛选场景：

1. **挑战性**: 天然构象必须从大量干扰构象中被正确识别
2. **多样性**: 包含不同类型的负样本（交叉构象和随机构象）
3. **实用性**: 反映实际药物发现中从候选构象库中筛选的情况
4. **统计可靠性**: 通过 >50 构象的限制确保评估的统计意义

### 3.5 与其他版本的对比

相比于v7和v8版本：
- **v4版本特点**: 对接构象完全不参与验证，专注于区分天然构象与非相关构象
- **评估纯度**: 避免了对接构象可能带来的"近似正确"干扰，评估更加纯粹
- **应用场景**: 更适合评估模型在完全未知结合模式情况下的识别能力

## 4. 虚拟筛选性能评估意义

### 4.1 实际应用价值

- **实用性评估**: Top-1成功率反映模型在实际应用中直接给出正确答案的能力
- **容错性评估**: Top-5成功率评估模型的容错能力，即使最佳预测不准确，正确答案仍在候选列表中
- **筛选效率**: 高的Top-N成功率意味着研究人员需要验证的候选构象更少，显著提高筛选效率

### 4.2 与传统指标的关系

- **与AUC的互补性**: AUC评估整体分类性能，而Top-N成功率更关注实际应用中的排序质量
- **与RMSD Loss的区别**: RMSD Loss关注预测精度，Top-N成功率关注排序准确性
- **实际意义**: 在药物发现中，研究人员通常只会选择排名最高的几个候选进行实验验证

### 4.3 评估标准

- **优秀性能**: Top-1 > 60%, Top-5 > 80%
- **可接受性能**: Top-1 > 40%, Top-5 > 60%
- **需要改进**: Top-1 < 30%, Top-5 < 50%

## 5. 总结

`valid_suc_top1` 和 `valid_suc_top5` 指标是评估蛋白质-配体对接模型在虚拟筛选任务中实际应用性能的关键指标。它们：

1. **模拟真实应用场景**: 反映模型在实际药物发现流程中的表现
2. **提供互补评估**: 与传统的损失函数和AUC指标形成互补，提供更全面的性能评估
3. **指导模型优化**: 帮助研究人员了解模型在排序任务上的强弱，指导进一步的模型改进

在v4版本的验证设置中，天然构象需要在一个包含大量交叉构象和随机构象的复杂环境中被正确识别，这种评估方式确保了训练出的模型不仅在统计指标上表现良好，更重要的是在实际虚拟筛选应用中具有实用价值。
