from typing import Dict, List
import numpy as np
from scipy import stats
import argparse
import os
import csv
def choose_best_pose(id_to_pred, select_option = "min"):
#     pairs = ["_".join(k.split("_")[:-1]) for k in id_to_pred.keys()]
    pairs = [k.split("_")[0] for k in id_to_pred.keys()]

    pairs = sorted(list(set(pairs)))
    retval = {p: [] for p in pairs}
    for key in id_to_pred.keys():
#         pair = "_".join(key.split("_")[:-1])
        pair = key.split("_")[0]

        retval[pair].append(id_to_pred[key])
    for key in retval.keys():
        if select_option == "min":
            retval[key] = np.min(retval[key])
        elif select_option == "max":
            retval[key] = np.max(retval[key])
        elif select_option == "mean":
            retval[key] = np.mean(retval[key])
    return retval


parser = argparse.ArgumentParser(description='dude calculate EF score')
parser.add_argument('--pred_dir', default=r'', type=str, help='')
parser.add_argument('--result_csv_path', default=r'', type=str, help='')

parser.add_argument('--prefer', default=r'negative', type=str, help='')
parser.add_argument('--select_option', default=r'min', type=str, help='')
parser.add_argument('--postfix', default=r'.txt', type=str, help='')

dude_dir = "/Arontier/People/junsuha/akscore_project/DUD_E"
dude_list_path = "/Arontier/People/hongyiyu/Data/akscore2/dude/id_list/dude_id_list.txt"

args = parser.parse_args()
print(args)



percents = [0.005, 0.01, 0.05]
result_stored = {
        "EF0.005":[],
        "EF0.01":[],
        "EF0.05":[],
    }


with open(dude_list_path) as f:
    target_names = f.readlines()

target_names = [line.strip('\n') for line in target_names]
target_names = sorted(target_names)



pred_filenames = [f for f in os.listdir(args.pred_dir) if f.endswith(args.postfix)]
pred_filenames = sorted(pred_filenames)
gt_dict = {}

for target_name in target_names:
    target_dir = os.path.join(dude_dir, target_name)
    gt_path = os.path.join(target_dir, "actives_final.ism")

    with open(gt_path, "r") as f:
        lines = f.readlines()
        lines = [line.strip('\n') for line in lines] ### remove \n
        gt_dict[target_name] = [line.split(" ")[-1] for line in lines]

if args.result_csv_path == "":
    result_csv_path = os.path.join(os.path.split(args.pred_dir)[0], "dude_EF.csv")
    
else:
    result_csv_path = args.result_csv_path



result_list = []
# for pred_filename in pred_filenames:
for target_name in target_names:

    target_result_list = []
    result = {
        "EF0.005":[],
        "EF0.01":[],
        "EF0.05":[],
    }
#     target_id = pred_filename.split(args.postfix)[0]
#     target_active_ids = gt_dict[target_id]
#     pred_path = os.path.join(args.pred_dir, pred_filename)
    
    
    target_id = target_name
    target_active_ids = gt_dict[target_id]
    pred_path = os.path.join(args.pred_dir, target_name+args.postfix)
    
    with open(pred_path, "r") as f:
        lines = f.readlines()
        lines = [line.split("\t") for line in lines]


    if lines[0][0].endswith('.pkl'):
        id_to_pred = {line[0][:-4]: float(line[2]) for line in lines}
    else:
        id_to_pred = {line[0]: float(line[2]) for line in lines}


    id_to_pred = choose_best_pose(id_to_pred, args.select_option)

    preds = list(id_to_pred.values())
    keys = list(id_to_pred.keys())
    idx = np.argsort(preds)

    if args.prefer == "positive":
        idx = idx[::-1]


    print(target_id)
    total_actives_num = len(target_active_ids)
    print(f"total actives num: {total_actives_num}")
    total_compounds_num = len(keys)
    
    target_result_list.append(target_id)
    for percent in percents:

        compounds_at_percent = round(total_compounds_num * percent)
        actives_at_percent = 0

        for key_idx in idx[:compounds_at_percent]:
            if keys[key_idx] in target_active_ids:
                # print(keys[key_idx])
                actives_at_percent +=1

        print(f"active num: {actives_at_percent}")

        result[f"EF{percent}"] = (actives_at_percent / compounds_at_percent) * (total_compounds_num / total_actives_num)
        result_stored[f"EF{percent}"].append(result[f"EF{percent}"])
        target_result_list.append(result[f"EF{percent}"])
    result_list.append(target_result_list)
    print(result)
    
    
print(f"average EF0.5%: {np.mean(result_stored['EF0.005'])}")
print(f"average EF1%: {np.mean(result_stored['EF0.01'])}")
print(f"average EF5%: {np.mean(result_stored['EF0.05'])}")

def write_to_csv(save_path, result_list):

    with open(save_path,'w') as f:
        writer = csv.writer(f)
        writer.writerow(['target_id', 'EF0.5%', 'EF1%', 'EF5%'])
        writer.writerows(result_list)
        writer.writerow(['average', np.mean(result_stored['EF0.005']), np.mean(result_stored['EF0.01']), np.mean(result_stored['EF0.05'])])


write_to_csv(result_csv_path, result_list)
