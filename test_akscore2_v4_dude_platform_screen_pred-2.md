# `test_akscore2_v4_dude_platform_screen_pred.py` 脚本输入文件结构说明

本文档旨在说明 `test_akscore2_v4_dude_platform_screen_pred.py` 脚本运行时，对输入数据的组织结构和格式的要求。这对于准备正确的输入文件至关重要。

## 核心参数

脚本通过命令行参数 `--screen_dir` 和 `--target_name` 来定位输入数据。

- `--screen_dir`: 指定包含所有靶点数据的根目录。
- `--target_name`: 指定本次运行要处理的具体靶点名称。这个名称对应于 `--screen_dir` 下的一个子目录。

## 期望的目录结构

根据对脚本的分析，它期望的输入文件目录结构如下：

```
<screen_dir>/
└── <target_name>/
    └── glide_graph_one/
        ├───complex_1.pkl.gz
        ├───complex_2.pkl.gz
        ├───... 
        └───complex_N.pkl.gz
```

### 结构解释

1.  **`<screen_dir>`**: 这是你在运行脚本时通过 `--screen_dir` 参数提供的主目录路径。
2.  **`<target_name>`**: 这是你在运行脚本时通过 `--target_name` 参数提供的靶点名称。脚本会在 `<screen_dir>` 目录下查找一个与该名称完全相同的子目录。
3.  **`glide_graph_one/`**: 在靶点目录 `<target_name>` 内部，必须存在一个名为 `glide_graph_one` 的子目录。脚本会直接进入这个目录去寻找输入文件。
    - *注意*: 脚本代码中注释掉了其他可能的子目录名（如 `graph` 或 `graph_one`），但当前激活的逻辑明确指向 `glide_graph_one`。
4.  **`*.pkl.gz` 文件**: `glide_graph_one` 目录中应包含一个或多个经过 gzip 压缩的 Pickle 文件，并以 `.pkl.gz` 作为扩展名。
    - 每一个 `.pkl.gz` 文件代表一个独立的蛋白-配体复合物图结构数据。
    - 脚本会遍历 `glide_graph_one` 目录下的所有 `.pkl.gz` 文件，加载其中的数据进行预测。
    - 文件名（不含扩展名）会被用作复合物的标识符（`name`）。

## 数据格式

- 输入文件必须是使用 `pickle` 序列化并随后用 `gzip` 压缩的 Python 对象。
- 每个 `.pkl.gz` 文件内部包含一个列表（list），这个列表中的每个元素都是一个 `torch_geometric.data.Data` 对象（或具有类似结构的图对象）。

## 示例

假设你有如下文件结构：

```
/home/<USER>/dude_data/
└── ace/
    └── glide_graph_one/
        ├───actives_1.pkl.gz
        ├───actives_2.pkl.gz
        └───decoys_1.pkl.gz
```

那么，你应该这样运行脚本：

```bash
python test_akscore2_v4_dude_platform_screen_pred.py \
    --screen_dir /home/<USER>/dude_data \
    --target_name ace \
    --model_path /path/to/your/model.pth \
    --result_save_dir /path/to/save/results
```

这样，脚本就会处理位于 `/home/<USER>/dude_data/ace/glide_graph_one/` 目录下的所有 `.pkl.gz` 文件。

```
