#!/bin/bash
#SBATCH --clusters=brain
#SBATCH --partition=akscore.q,gpu.q,gpu_48g.q
###### ### #SBATCH --nodelist=dive513
#SBATCH --exclude=dive602,dive507
#SBATCH --qos=normal
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=0
#SBATCH --job-name=train_1422_5A_v7_br_nl5_hd256_do10_ced8_general
#SBATCH --output=%x.out
#SBATCH --error=%x.err

echo "----------"
echo "HOSTNAME = ${HOSTNAME}"
echo "SLURM_JOB_NAME = ${SLURM_JOB_NAME}"
echo "SLURM_CPUS_PER_TASK = ${SLURM_CPUS_PER_TASK}"
echo "CUDA_VISIBLE_DEVICES = ${CUDA_VISIBLE_DEVICES}"
echo "----------"


# #### train_1422_5A_v8_nl5_hd256_do10_ced8_general
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v8.py --hidden_dim 256 --num_layers 5 --dropout 0.1 --cut_edge_dis 8 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220829.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220829.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_340_tl_2.259_vl_11.800_vs1_0.552_vs5_0.715.pth 

# # # ##### train_1422_5A_v7_br_nl5_hd256_do10_ced8_general
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v7_bind_rmsd.py --hidden_dim 256 --num_layers 5 --dropout 0.1 --cut_edge_dis 8 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220829.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220829.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_400_tl_1.895_vl_7.365_vs1_0.687_vs5_0.826.pth 

# # # # ##### train_122_5A_v4_nl5_hd256_do10_general
python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v4.py --node_dim 72 --edge_dim 12 --hidden_dim 256 --num_layers 5 --dropout 0.1 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220829.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220829.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_390_tl0.049_tc0.997_vl0.037_vc0.972.pth

















# #### train_1422_5A_v8_nl5_hd512_do10_ced8_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v8.py --hidden_dim 512 --num_layers 5 --dropout 0.1 --cut_edge_dis 8 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v8_best_5 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v8_best_5/lr0.0001_bs4_nd73_ed24_hd512_nl5_do0.1_ced8/checkpoint_750_tl_0.645_vl_15.400_vs1_0.486_vs5_0.639.pth


# # #### train_1422_5A_v7_br_nl5_hd512_do10_ced8_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v7_bind_rmsd.py --hidden_dim 512 --num_layers 5 --dropout 0.1 --cut_edge_dis 8 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v7_br_best_1 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v7_br_best_1/lr0.0001_bs4_nd73_ed24_hd512_nl5_do0.1_ced8/checkpoint_750_tl_1.131_vl_11.115_vs1_0.406_vs5_0.631.pth






# #### train_1422_5A_v6_no_atd_nl5_hd128_do25_ced8_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v6_no_atd.py --hidden_dim 256 --num_layers 5 --dropout 0.1 --cut_edge_dis 8 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v6_no_atd_best --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --lr 0.0001 

# # #### train_1422_5A_v6_limit30_nl5_hd128_do25_ced4_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v6.py --hidden_dim 128 --num_layers 5 --dropout 0.25 --cut_edge_dis 4 --num_workers 8 --batch_size 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v6_limit30_best --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --atd_score_path /Arontier/People/junsuha/akscore_project/sim_code/refined_all_atscore.txt --lr 0.0001 




#### train_1422_5A_v5_mean_nl5_hd256_do25_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v5_mean.py --hidden_dim 256 --num_layers 5 --dropout 0.25 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v5_mean_best --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --atd_score_path /Arontier/People/junsuha/akscore_project/sim_code/refined_all_atscore.txt --lr 0.0001 


# # #### train_1422_5A_v5_nl5_hd256_do25_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v5.py --hidden_dim 256 --num_layers 5 --dropout 0.25 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/1422_5A_v5_best --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --atd_score_path /Arontier/People/junsuha/akscore_project/sim_code/refined_all_atscore.txt --lr 0.0001 











# # ##### train_122_5A_v4_nl5_hd128_refine_best
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v4_only_screening_2_platform.py --node_dim 72 --edge_dim 12 --hidden_dim 128 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220726/screening_ratio122_5A_v4_random --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all_random --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --lr 0.0001 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220726/screening_ratio122_5A_v4_random/lr0.0001_bs32_nd72_ed12_hd128_nl5/checkpoint_300_tl0.141_tc0.981_vl0.095_vc0.957.pth


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v4_only_screening_2.py --hidden_dim 128 --num_layers 5 --num_workers 8 --batch_size 32  --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220715_reg/screening_ratio144_5A_v4 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715.txt --lr 0.0001 

# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening_pignet_2.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220715_reg/screening_ratio1188_5A_pignet --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715.txt --lr 0.0001

# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening_rmsd_2.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220715_reg/screening_ratio1188_5A_rmsd --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715.txt --lr 0.0001 


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening_pignet_2.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220715_reg/screening_ratio1188_5A_pignet_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined_all --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_list_5A_220715_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_list_5A_220715_refine.txt --lr 0.0001






# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v4_only_screening.py --node_dim 72 --edge_dim 12 --hidden_dim 128 --num_layers 5 --num_workers 8 --batch_size 32  --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio122_5A_v4_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --lr 0.0001 


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v3_only_screening.py --hidden_dim 128 --num_layers 3 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_5A_v3_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.0001 



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v3_only_docking.py --hidden_dim 128 --num_layers 3 --num_workers 8 --batch_size 32 --cut_edge_dis 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/docking_ratio19_5A_v3_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.0001 


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v3_only_scoring.py --hidden_dim 128 --num_layers 3 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/scoring_5A_v3_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.0001 



###### train_screen1144_5A_v2_ce8_pignet_lr0001_ft
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening_pignet_loss.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_5A_refine_pignet_loss --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --lr 0.0001



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening_rmsd.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_8A_refine_rmsd --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_8A_redock_cross_refined --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_8A_220713_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_8A_220713_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_8A_220713_refine.txt --lr 0.0001

# ###### train_screen1144_5A_v2_ce8_lr0001_ft
# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_screening.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --ignore_edge_dis 0 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/screening_ratio1144_5A_refine_finetune_native --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.00005 --resume_model_path /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/scoring_5A_refine/lr0.0001_bs32_nd73_ed24_hd256_nl5_ce8/checkpoint_100_tl3.583_tc0.724_vl3.458_vc0.688.pth 



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_docking.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 4 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/docking_ratio19_5A_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.0001 



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_only_scoring.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --cut_edge_dis 8 --ignore_edge_dis 1 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220701_reg/scoring_5A_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_5A_redock_cross_refined2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_5A_220630_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_5A_220630_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_5A_220630_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/data_5A_redock_cross_refined2_no_protein_4A.txt --lr 0.0001 



















# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_pignet_data_pignet_loss.py --hidden_dim 128 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220614_reg/pignet_data_loss_ratio1111_adam0001_nd35 --data_dir /Arontier/People/junsuha/akscore_project/pignet_data/data --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pignet_pdbid_list.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pignet_pdbid_list.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pignet_pdbid_list.txt --lr 0.0001 --node_dim 35

# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220616_reg/ratio112_5A_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new4 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new4_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new4_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new4_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --lr 0.0001

# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220616_reg/ratio112_8A_refine --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new3 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new3_refine.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new3_refine.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new3_refine.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --lr 0.0001


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name_no_rmsd_aug.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220608_reg/ratio111_no_norm_no_rmsd_no_screen_aug --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new2.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new2.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new2.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --coreset_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org2.pkl.gz 



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220608_reg/ratio111_no_norm_rmsd10_filtered_10_40 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new2_rmsd_10_40_filtered.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new2_rmsd_10_40_filtered.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new2.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --coreset_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org2.pkl.gz 



# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name_no_rmsd_aug.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220608_reg/ratio111_no_norm_no_rmsd_aug_filtered_10_40 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new2_rmsd_10_40_filtered.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new2_rmsd_10_40_filtered.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new2.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --coreset_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org2.pkl.gz 


# python -u /Arontier/People/hongyiyu/Project/akscore2/train_akscore2_v2_by_name_aug.py --hidden_dim 256 --num_layers 5 --num_workers 8 --batch_size 32 --model_save_dir /Arontier/People/hongyiyu/Data/akscore2/model_220608_reg/ratio111_no_norm_aug_filtered_10_40 --data_dir /Arontier/People/junsuha/akscore_project/sim_code/data_new2 --train_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/train_pdbid_list_data_new2_rmsd_10_40_filtered.txt --valid_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/valid_pdbid_list_data_new2_rmsd_10_40_filtered.txt --test_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/test_pdbid_list_data_new2.txt --exclude_list_path /Arontier/People/hongyiyu/Data/akscore2/train_valid_list/exclude_pdbid_list_data_new2.txt --coreset_path /Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org2.pkl.gz 
