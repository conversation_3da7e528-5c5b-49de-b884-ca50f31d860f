# test_akscore2_v7_dude_screen_pred.py 技术文档

## 1. 脚本运行逻辑分析

### 1.1 整体流程
该脚本实现了使用训练好的AKScore2 v7模型对DUD-E数据集中的蛋白质-配体复合物进行虚拟筛选的功能。完整执行流程如下：

1. **参数解析**：通过`argparse`解析命令行参数，包括数据路径、目标蛋白名称和模型超参数
2. **模型初始化**：创建`GATv2`模型实例并加载预训练权重
3. **数据目录配置**：确定结果保存目录和目标蛋白名称
4. **循环处理目标蛋白**：对每个目标蛋白执行以下步骤：
   - 初始化结果字典
   - 构建目标数据路径
   - 创建数据集和数据加载器
   - 执行模型预测
   - 保存预测结果

### 1.2 数据处理管道
脚本中的数据处理管道由以下阶段组成：

1. **数据扫描**：扫描指定目录下所有`.pkl.gz`文件，这些文件包含预处理好的蛋白质-配体复合物图数据
2. **数据加载**：通过`gzip.open`和`pickle.load`读取压缩的pickle文件，获取图数据对象
3. **图结构分解**：使用`graph_modification`函数将复合物图分解为：
   - 完整图(`graph`)：原始蛋白质-配体复合物图
   - 蛋白质图(`protein_graph`)：仅包含蛋白质原子和内部相互作用
   - 配体图(`ligand_graph`)：仅包含配体原子和内部相互作用
   - 蛋白质-配体交互图(`protein_ligand_graph`)：包含所有原子和蛋白质-配体交互
4. **批次处理**：使用PyTorch Geometric的`Batch.from_data_list`将多个图对象合并为批次

### 1.3 模型预测过程
模型预测过程包括以下步骤：

1. **特征提取**：模型通过`GATv2Conv`层处理图数据，提取节点特征
2. **全局池化**：使用`global_mean_pool`将节点特征聚合为图级特征
3. **多任务预测**：模型生成两个预测值：
   - `bind_logits`：结合亲和力预测（以-logKd/Ki为单位，值越低表示结合亲和力越强）
   - `rmsd_logits`：RMSD(均方根偏差)预测（值越低表示构象越接近天然构象）
4. **组合预测**：将两个预测值相加得到最终的`bind_rmsd_logits`作为筛选得分，采用以下计算方式：
   ```python
   bind_rmsd_logits = bind_logits + rmsd_logits
   ```
   这种组合方式的特点：
   - 同时考虑结合强度和构象质量两个关键因素
   - 采用1:1的权重比例，认为两个因素同等重要
   - 最终得分越低越好，表示同时具有强的结合亲和力和好的结合构象
   - 这是v7版本（DockS）的特色设计，与v4（只考虑姿势分类）和v8（将RMSD信息编码到结合能中）的策略不同
5. **结果收集**：收集所有预测得分和对应的配体名称

注意：结合亲和力预测值经过了标准化处理（`BIND_AFF_MEAN = -0` 和 `BIND_AFF_STD = 1`），这个设计假设两个预测值的尺度是可比的。如果实际上两个值的数值范围差异很大，可能需要额外的标准化处理。

## 2. 输入文件架构详解

### 2.1 目录结构要求
输入数据的目录结构由以下参数决定：

- `--screen_dir`：DUD-E数据集的根目录，默认为`/Arontier/People/junsuha/akscore_project/DUD_E`
- `--target_name`：目标蛋白的名称，如"akt1"、"bace1"等

完整的目录结构如下：
```
screen_dir/
└── target_name/                  # 目标蛋白目录（如"akt1"）
    └── glide_graph_one/          # 经过Glide处理的图数据目录
        ├── compound1.pkl.gz      # 压缩的图数据文件
        ├── compound2.pkl.gz
        └── ...
```

### 2.2 pkl.gz文件内部结构
每个`.pkl.gz`文件内部包含一个由`pickle.dump`序列化后经gzip压缩的Python对象列表，这个列表包含一个或多个PyTorch Geometric的`Data`对象：

```python
# 文件加载代码
with gzip.open(graph_path, 'rb') as f:
    pdb_id_graphs = pickle.load(f)  # 返回Data对象列表
```

### 2.3 图数据格式
PyTorch Geometric的`Data`对象包含以下主要属性：

- **`x`**: 节点特征矩阵，维度为[N, 73]
  - N是原子数量
  - 73维特征包括原子类型、电荷、杂化状态等信息

- **`edge_index`**: 边索引矩阵，维度为[2, M]
  - M是边的数量
  - 第一行是源节点索引，第二行是目标节点索引

- **`edge_attr`**: 边特征矩阵，维度为[M, 24]
  - 24维特征描述边的属性
  - 前3维[1,0,0]、[0,1,0]、[0,0,1]分别表示蛋白质内部边、配体内部边和蛋白质-配体相互作用边
  - 后续维度可能包括距离、角度等信息

- **`name`**: 配体名称，用于识别和结果输出

### 2.4 数据加载过程
`akscore2_dataset`类负责数据加载，其关键方法包括：

- **`__init__`**: 扫描目录下所有`.pkl.gz`文件，构建文件路径列表
- **`len`**: 返回数据集中样本数量（文件数量）
- **`graph_modification`**: 将复合物图分解为蛋白质图、配体图和交互图
- **`get`**: 加载指定索引的数据，并返回批次化后的图数据

## 3. 输出文件架构和内容

### 3.1 输出目录结构
输出目录结构由以下逻辑确定：

1. 如果`--result_save_dir`未指定，则使用模型路径（去除`.pth`后缀）作为基础目录
2. 在基础目录下创建`glide_sp_bind_rmsd`子目录
3. 最终结果文件保存在`{result_save_dir}/glide_sp_bind_rmsd/{target_name}.txt`

例如：
```
result_save_dir/
└── glide_sp_bind_rmsd/
    ├── akt1.txt
    ├── bace1.txt
    └── ...
```

### 3.2 文件命名规则
输出文件的命名规则很简单：
- 目录名：固定为`glide_sp_bind_rmsd`，表示使用Glide SP对接方法，并使用结合亲和力和RMSD组合得分
- 文件名：`{target_name}.txt`，其中`target_name`是目标蛋白的名称

### 3.3 文件内容格式
输出文件的格式为制表符分隔的文本文件，每行包含三列：
```
{name}\t{i}\t{bind_rmsd_preds[i]:.4f}\n
```

其中：
- `name`：配体名称，从原始文件名中提取并经过处理（`name.split("_")[0] + "_" + name.split("_")[-1]`）
- `i`：配体在结果列表中的索引
- `bind_rmsd_preds[i]`：配体的预测得分（结合亲和力和RMSD的组合得分），保留4位小数。得分越低表示配体越好，同时具有强的结合亲和力和好的结合构象

### 3.4 结果后处理
`save_result()`函数负责后处理和结果保存：

1. 创建结果保存目录
2. 构建完整的输出文件路径
3. 遍历结果字典中的预测值和配体名称
4. 处理配体名称（只保留第一个和最后一个下划线分隔的部分）
5. 将处理后的配体名称、索引和预测得分写入文件

## 4. 关键参数影响分析

### 4.1 路径相关参数
- **`--screen_dir`**: 决定输入数据的根目录位置，关系到整个数据访问路径
- **`--target_name`**: 指定要筛选的目标蛋白，决定访问数据的子目录
- **`--result_save_dir`**: 决定结果文件的保存位置，如未指定则使用模型路径作为默认值
- **`--model_path`**: 指定预训练模型的路径，同时可能影响默认的结果保存路径

### 4.2 模型相关参数
- **`--node_dim`**: 节点特征维度（默认73），必须与训练时一致
- **`--edge_dim`**: 边特征维度（默认24），必须与训练时一致
- **`--hidden_dim`**: 隐藏层维度（默认256），影响模型容量
- **`--num_layers`**: GNN层数（默认5），影响特征提取深度
- **`--dropout`**: Dropout比率（默认0.25），影响推理时的行为
- **`--cut_edge_dis`**: 边距离截断（默认8Å），影响使用哪些原子间相互作用

### 4.3 数据加载参数
- **`--batch_size`**: 批处理大小（默认4），影响内存使用和处理速度
- **`--num_workers`**: 数据加载器工作进程数（默认4），影响数据加载速度

## 实际使用示例

### 运行脚本示例
```bash
python test_akscore2_v7_dude_screen_pred.py \
    --screen_dir /path/to/DUD_E \
    --target_name akt1 \
    --result_save_dir /path/to/results \
    --model_path /path/to/model.pth \
    --node_dim 73 \
    --edge_dim 24 \
    --hidden_dim 256 \
    --num_layers 5 \
    --batch_size 4
```