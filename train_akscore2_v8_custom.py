import os, pickle
import numpy as np
import argparse
from sklearn.metrics import mean_squared_error, mean_absolute_error, auc
from torch_geometric.nn import global_mean_pool
from torch_geometric.loader import DataLoader
from torch_geometric.data import Data
from sklearn.metrics import roc_auc_score
import torch
import torch.nn.functional as F
from torch import nn
from torch.nn import Linear
from torch_geometric.nn import GATv2Conv
from torch_geometric.data import Dataset
from torch_geometric.data import batch as pyg_batch_func
import random
import time
import gzip
from itertools import chain
from tqdm import tqdm

#a=1

DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# DEVICE = torch.device('cpu')

"""
基于图注意力网络v2 (GATv2) 的蛋白质-配体相互作用预测模型。

该模型旨在预测蛋白质与配体之间的结合亲和力。它采用一体化处理方式，
直接在蛋白质-配体复合物的完整图结构上进行学习。
"""
class GATv2(torch.nn.Module):
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64, dropout=0.25, cut_edge_dis=8):
        """
        GATv2模型的初始化函数。

        Args:
            node_dim (int): 节点特征的维度。
            edge_dim (int): 边特征的维度。
            num_layers (int, optional): GNN层的数量。默认为 5。
            hidden_dim (int, optional): 隐藏层的维度。默认为 64。
            dropout (float, optional): Dropout的比率。默认为 0.25。
            cut_edge_dis (int, optional): 用于筛选边的距离阈值(Å)。默认为 8。
        """
        super().__init__()
        self.num_layers = num_layers
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.cut_edge_dis = cut_edge_dis

        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(dropout)

        self.protein_ligand_gnn_layers = []
        for num in range(self.num_layers):
            self.protein_ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_ligand_gnn_layers = nn.ModuleList(self.protein_ligand_gnn_layers)

        self.graph_dec_bind = nn.Sequential(nn.Linear(self.hidden_dim, self.hidden_dim),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim // 2, 1),
                                             )


    def forward(self, graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch):
        """
        定义模型的前向传播逻辑。

        Args:
            graph_batch (torch_geometric.data.Batch): 包含整个复合物图的批处理对象。
            protein_graph_batch (torch_geometric.data.Batch): 包含蛋白质图的批处理对象 (当前版本未使用)。
            ligand_graph_batch (torch_geometric.data.Batch): 包含配体图的批处理对象 (当前版本未使用)。
            protein_ligand_graph_batch (torch_geometric.data.Batch): 包含蛋白质-配体相互作用图的批处理对象 (当前版本未使用)。

        Returns:
            torch.Tensor: 预测的结合亲和力 logits。
        """

        x, edge_index, edge_attr, batch = graph_batch.x, graph_batch.edge_index, graph_batch.edge_attr, graph_batch.batch
        protein_x, protein_edge_index, protein_edge_attr, protein_batch = protein_graph_batch.x, protein_graph_batch.edge_index, protein_graph_batch.edge_attr, protein_graph_batch.batch
        ligand_x, ligand_edge_index, ligand_edge_attr, ligand_batch = ligand_graph_batch.x, ligand_graph_batch.edge_index, ligand_graph_batch.edge_attr, ligand_graph_batch.batch
        protein_ligand_x, protein_ligand_edge_index, protein_ligand_edge_attr, protein_ligand_batch = protein_ligand_graph_batch.x, protein_ligand_graph_batch.edge_index, protein_ligand_graph_batch.edge_attr, protein_ligand_graph_batch.batch


        x = self.node_to_hidden(x)



        #### filtering edge index by distance 8A 6A 4A
        if self.cut_edge_dis == 6:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-1] != torch.Tensor([0, 0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]
        elif self.cut_edge_dis == 4:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-2] != torch.Tensor([0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]

        for layer in self.protein_ligand_gnn_layers:
            x = layer(x, edge_index, edge_attr)
            x = F.relu(x)
            x = self.dropout_layer(x)


        protein_ligand_x_dock = global_mean_pool(x, batch)  # [batch_size, hidden_channels]

        bind_logits = self.graph_dec_bind(protein_ligand_x_dock)

        return bind_logits


"""
自定义数据集类，用于加载和预处理蛋白质-配体复合物图数据。
"""
class akscore2_dataset(Dataset):
    def __init__(self, data_dir, pdb_id_list_path, exclude_list_path=None, train_mode=True,
                 rmsd_cutoff=2, bind_aff_aug=[-0.5, 0.5], bind_aff_rej=[-6, 0]):
        """
        akscore2_dataset的初始化函数。

        该函数负责扫描数据目录，根据PDB ID列表加载文件路径，
        并将它们按类型（native, dock, cross, random）组织起来。

        Args:
            data_dir (str): 数据集所在的根目录。
            pdb_id_list_path (str): 包含PDB ID列表的文本文件路径。
            exclude_list_path (str, optional): 需要从数据集中排除的PDB ID列表文件路径。默认为 None。
            train_mode (bool, optional): 是否为训练模式。这会影响数据的采样策略。默认为 True。
            rmsd_cutoff (int, optional): RMSD阈值，用于定义“好”构象 (未使用在此版本)。默认为 2。
            bind_aff_aug (list, optional): 结合亲和力数据增强的范围 (未使用在此版本)。默认为 [-0.5, 0.5]。
            bind_aff_rej (list, optional): 结合亲和力拒绝的范围 (未使用在此版本)。默认为 [-6, 0]。
        """
        super(akscore2_dataset, self).__init__()
        self.train_mode = train_mode
        self.data_dir = data_dir

        self.rmsd_cutoff = rmsd_cutoff
        self.bind_aff_aug = bind_aff_aug
        self.bind_aff_rej = bind_aff_rej

        self.path_dict_list = []

        with open(pdb_id_list_path) as f:
            self.pdb_id_list = f.readlines()

        self.pdb_id_list = [line.strip('\n') for line in self.pdb_id_list]
        if exclude_list_path:
            with open(exclude_list_path) as f:
                exclude_pdb_id_list = f.readlines()
            exclude_pdb_id_list = [line.strip('\n') for line in exclude_pdb_id_list]
            self.pdb_id_list = list(set(self.pdb_id_list) - set(exclude_pdb_id_list))

        for pdb_id in tqdm(self.pdb_id_list, desc="索引数据文件"):
            pdb_id_dir = os.path.join(self.data_dir, pdb_id)
            pdb_id_filenames = [f for f in os.listdir(pdb_id_dir) if f.endswith('.pkl')]
            dump_paths = {
                "native": [],
                "dock": [],
                "cross": [],
                "random": [],
            }
            for pdb_id_filename in pdb_id_filenames:
                pdb_id_file_path = os.path.join(pdb_id_dir, pdb_id_filename)
                name_splited = pdb_id_filename.split('_')

                if name_splited[-2] == '0':
                    dump_paths["native"].append(pdb_id_file_path)
                elif name_splited[-2] == '1':
                    dump_paths["dock"].append(pdb_id_file_path)
                elif name_splited[-2] == '2':
                    dump_paths["cross"].append(pdb_id_file_path)
                elif name_splited[-2] == '3':
                    dump_paths["random"].append(pdb_id_file_path)

            self.path_dict_list.append(dump_paths)


        self.len_dataset = len(self.path_dict_list)

    def len(self):
        """返回数据集中的PDB ID总数。"""
        #         return 30
        return self.len_dataset



    def graph_rmsd_bindaff_aug(self, graph):
        """
        对图数据进行标签增强的核心函数。

        对于对接构象（docked decoys, 类型 '1'），此函数会将其RMSD值
        加到其结合亲和力标签上。这是一种巧妙的技巧，将姿势的准确性信息
        （RMSD）编码到结合亲和力的学习目标中，从而惩罚不准确的对接姿势。

        Args:
            graph (torch_geometric.data.Data): 单个图数据对象。

        Returns:
            torch_geometric.data.Data: 经过标签增强的图数据对象。
        """

        graph.bind = torch.as_tensor(graph.bind, dtype=torch.float32).unsqueeze(-1).unsqueeze(-1)
        graph.y = graph.y.float()

        name_splited = graph.name.split('_')
        if name_splited[-2] == '1': ###if decoy dock
            graph.bind = graph.bind + graph.y


        return graph

    def graph_modification(self, graph):
        """
        将完整的蛋白质-配体复合物图分解为独立的子图。

        虽然此v8模型在GNN中采用一体化处理，但此函数仍然保留用于
        数据结构的分解，以备后续可能使用。它将复合物图分解为：
        蛋白质图、配体图和蛋白质-配体相互作用图。

        Args:
            graph (torch_geometric.data.Data): 原始的复合物图。

        Returns:
            tuple: 包含四个图对象的元组 (原始图, 蛋白质图, 配体图, 相互作用图)。
        """
        x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()

        protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
        ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
        protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]

        protein_edge_index = edge_index[:, protein_edge_attr_idx]
        ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
        protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]

        protein_ligand_node_sep_idx = torch.min(ligand_edge_index)

        protein_x = x[:protein_ligand_node_sep_idx, :]
        ligand_x = x[protein_ligand_node_sep_idx:, :]

        protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx, :])
        ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index - torch.min(ligand_edge_index),
                            edge_attr=edge_attr[ligand_edge_attr_idx, :])

        protein_ligand_edge_attr = edge_attr[protein_ligand_edge_attr_idx, :]

        protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)

        return graph, protein_graph, ligand_graph, protein_ligand_graph

    def get(self, idx):
        """
        根据索引获取并处理一个数据样本（或一个样本集合）。

        这是PyTorch Dataset类的核心方法。它根据当前是训练模式还是
        验证/测试模式，采用不同的数据采样策略。
        - 训练模式: 为每个天然构象（native），随机采样4个对接（dock）、
          2个交叉（cross）和2个随机构象（random）来构建一个具有挑战性的
          “样本集合”，用于训练。
        - 验证/测试模式: 加载指定PDB ID下的所有类型的构象。

        所有加载的图都会经过标签增强和图分解。

        Args:
            idx (int): 数据集的索引。

        Returns:
            tuple: 包含四个批处理图对象的元组，分别对应
                   (完整图, 蛋白质图, 配体图, 相互作用图)。
        """

        pdb_id_graphs = []
        iter_graph_paths = []
        if self.train_mode:
            iter_graph_paths.extend(self.path_dict_list[idx]["native"])
            ########### sampling ratio
            for random_i in random.sample(range(0, self.len_dataset), 4):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["dock"], 1))
            for random_i in random.sample(range(0, self.len_dataset), 2):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["cross"], 1))
            for random_i in random.sample(range(0, self.len_dataset), 2):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["random"], 1))


            for iter_graph_path in iter_graph_paths:
                with open(iter_graph_path, 'rb') as f:
                    pdb_id_graph = pickle.load(f)
                pdb_id_graphs.append(pdb_id_graph)

        else:

            iter_graph_paths.extend(self.path_dict_list[idx]["native"])
            iter_graph_paths.extend(self.path_dict_list[idx]["dock"])
            iter_graph_paths.extend(self.path_dict_list[idx]["cross"])
            iter_graph_paths.extend(self.path_dict_list[idx]["random"])

            for iter_graph_path in iter_graph_paths:
                with open(iter_graph_path, 'rb') as f:
                    pdb_id_graph = pickle.load(f)
                pdb_id_graphs.append(pdb_id_graph)

        graph_list = []
        protein_graph_list = []
        ligand_graph_list = []
        protein_ligand_graph_list = []
        for graph in pdb_id_graphs:
            graph = self.graph_rmsd_bindaff_aug(graph)
            graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)

            graph_list.append(graph)
            protein_graph_list.append(protein_graph)
            ligand_graph_list.append(ligand_graph)
            protein_ligand_graph_list.append(protein_ligand_graph)

        graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
        protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
        ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
        protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)

        return graph_list_batch, protein_graph_list_batch, ligand_graph_list_batch, protein_ligand_graph_list_batch




def get_data_type(name_list):
    """
    根据图的名称，将一个批次中的数据索引按类型分类。

    文件名中包含一个类型代码（_0_, _1_, _2_, _3_），此函数
    解析这些代码，并将批次中每个图的索引存放到对应的字典键中。

    Args:
        name_list (list): 包含批次中所有图名称的列表。

    Returns:
        dict: 一个字典，键为 "native", "dock", "cross", "random"，
              值为对应类型的图在批次中的索引列表。
    """
    data_type_dict_batch = {
        "native": [],
        "dock": [],
        "cross": [],
        "random": [],
    }
    name_list = list(chain(*name_list))
    for name_i, name in enumerate(name_list):
        name_splited = name.split('_')
        if name_splited[-2] == '0':
            data_type_dict_batch["native"].append(name_i)
        elif name_splited[-2] == '1':
            data_type_dict_batch["dock"].append(name_i)
        elif name_splited[-2] == '2':
            data_type_dict_batch["cross"].append(name_i)
        elif name_splited[-2] == '3':
            data_type_dict_batch["random"].append(name_i)
    return data_type_dict_batch


def valid_model_epoch(model, loader, criterion, cr_loss_weight=5.0):
    """
    在验证集上评估模型一个周期的性能。

    该函数不进行反向传播或参数更新，只计算验证损失和评估指标，
    例如top-1和top-5成功率。

    Args:
        model (torch.nn.Module): 待评估的模型。
        loader (torch.utils.data.DataLoader): 验证数据加载器。
        criterion (dict): 包含损失函数的字典。

    Returns:
        dict: 包含该周期验证过程中的平均损失和各项评估指标的字典。
    """
    model.eval()
    epoch_loss = {
        "total": 0.0,
        "bind_native": 0.0,
        "bind_cross_random": 0.0,
        "bind_dock": 0.0,
        "suc_top1": 0.0,
        "suc_top5": 0.0,
    }


    success_count = {
        "top1": 0.0,
        "top5": 0.0,
    }

    target_count = 0
    with torch.no_grad():

        for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):

            data_type_dict_batch = get_data_type(graph_batch.name)

            bind_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE),
                                             ligand_graph_batch.to(DEVICE),
                                             protein_ligand_graph_batch.to(DEVICE))

            ### binding affinity mse loss
            loss_bind_native_mse = criterion['mse'](bind_logits[data_type_dict_batch["native"]],
                                                    graph_batch.bind[data_type_dict_batch["native"]])
            loss_bind_cross_random = -5 - bind_logits[data_type_dict_batch["cross"] + data_type_dict_batch["random"]]
            loss_bind_cross_random = loss_bind_cross_random.clamp(min=0.0).mean() * cr_loss_weight  ### add weight

            loss_bind_dock_mse = criterion['mse'](bind_logits[data_type_dict_batch["dock"]],
                                                    graph_batch.bind[data_type_dict_batch["dock"]])


            total_loss = loss_bind_native_mse + loss_bind_cross_random + loss_bind_dock_mse

            print(f"Validatoin: {idx}/{len(loader)}, total: {total_loss:.4f}, bind native: {loss_bind_native_mse:.4f}, "
                  f"bind cross random: {loss_bind_cross_random:.4f}, bind dock: {loss_bind_dock_mse:.4f},")
            epoch_loss["total"] += total_loss.item()
            epoch_loss["bind_native"] += loss_bind_native_mse.item()
            epoch_loss["bind_cross_random"] += loss_bind_cross_random.item()
            epoch_loss["bind_dock"] += loss_bind_dock_mse.item()

            native_index = data_type_dict_batch["native"][0]
            #### success rate in top n
            top_list = torch.topk(bind_logits, 10, dim=0, largest=False).indices.squeeze().tolist()
            target_count = target_count + 1
            if native_index in top_list:
                success_count["top5"] += 1
            if native_index in top_list[:3]:
                success_count["top1"] += 1

    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key] / len(loader)

    epoch_loss["suc_top1"] = success_count["top1"] / target_count
    epoch_loss["suc_top5"] = success_count["top5"] / target_count

    print(f'top1 {success_count["top1"] / target_count}')
    print(f'top5 {success_count["top5"] / target_count}')


    return epoch_loss


def train_model_epoch(model, loader, criterion, optimizer, cr_loss_weight=5.0):
    """
    在训练集上训练模型一个周期。

    该函数执行完整的训练步骤：前向传播、计算损失、反向传播和优化器更新。
    损失函数是针对不同数据来源的复合损失。

    Args:
        model (torch.nn.Module): 待训练的模型。
        loader (torch.utils.data.DataLoader): 训练数据加载器。
        criterion (dict): 包含损失函数的字典，例如 'mse'。
        optimizer (torch.optim.Optimizer): 优化器，例如 Adam。

    Returns:
        tuple:
            - torch.nn.Module: 训练一个周期后的模型。
            - dict: 包含该周期训练过程中的平均损失值的字典。
    """
    model.train()
    epoch_loss = {
        "total": 0.0,
        "bind_native": 0.0,
        "bind_cross_random": 0.0,
        "bind_dock": 0.0,
    }

    for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):

        data_type_dict_batch = get_data_type(graph_batch.name)



        bind_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE),
                                         ligand_graph_batch.to(DEVICE),
                                         protein_ligand_graph_batch.to(DEVICE))

        ### binding affinity mse loss
        loss_bind_native_mse = criterion['mse'](bind_logits[data_type_dict_batch["native"]],
                                                graph_batch.bind[data_type_dict_batch["native"]])
        loss_bind_cross_random = -5 - bind_logits[data_type_dict_batch["cross"] + data_type_dict_batch["random"]]
        loss_bind_cross_random = loss_bind_cross_random.clamp(min=0.0).mean() * cr_loss_weight  ### add weight

        loss_bind_dock_mse = criterion['mse'](bind_logits[data_type_dict_batch["dock"]],
                                                graph_batch.bind[data_type_dict_batch["dock"]])


        total_loss = loss_bind_native_mse + loss_bind_cross_random + loss_bind_dock_mse
        
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()

        epoch_loss["total"] += total_loss.item()
        epoch_loss["bind_native"] += loss_bind_native_mse.item()
        epoch_loss["bind_cross_random"] += loss_bind_cross_random.item()
        epoch_loss["bind_dock"] += loss_bind_dock_mse.item()

        print(f"Train: {idx}/{len(loader)}, total: {total_loss:.4f}, bind native: {loss_bind_native_mse:.4f}, "
              f"bind cross random: {loss_bind_cross_random:.4f}, bind dock: {loss_bind_dock_mse:.4f},")


    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key] / len(loader)

    return model, epoch_loss


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Graph based Protein-Ligand Binding Affinity Regression')
    parser.add_argument('--data_dir', default=r'', type=str, help='data directory')
    parser.add_argument('--train_list_path', default=r'', type=str, help='train list path')
    parser.add_argument('--valid_list_path', default=r'', type=str, help='validation list path')

    parser.add_argument('--exclude_list_path', default=r'', type=str, help='exclude list path')

    parser.add_argument('--model_save_dir', default=r'', type=str, help='model save directory')
    parser.add_argument('--resume_model_path', default=r'', type=str, help='resume model path')
    parser.add_argument('--num_workers', default=8, type=int, help="cpu worker number")
    parser.add_argument('--batch_size', default=4, type=int, help='batch size')

    parser.add_argument('--lr', default=0.0001, type=float, help='Learnig Rate')
    parser.add_argument('--node_dim', default=73, type=int, help="Input size of layer's node")
    parser.add_argument('--edge_dim', default=24, type=int, help='edge dimension')


    parser.add_argument('--hidden_dim', default=128, type=int, help='Hidden layer size')
    parser.add_argument('--num_layers', default=5, type=int, help='number of gnn layer')
    parser.add_argument('--dropout', default=0.25, type=float, help='dropout ratio')
    parser.add_argument('--cut_edge_dis', default=8, type=int, help='non-covalent bond edge distance')



    parser.add_argument('--rmsd_cutoff', default=2, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_aug_upper', default=1, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_aug_lower', default=-1, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_rej_upper', default=0, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_rej_lower', default=-5, type=int, help='number of gnn layer')

    parser.add_argument('--max-epochs', default=5000, type=int, help='maximum number of training epochs')
    parser.add_argument('--cr-loss-weight', default=5.0, type=float, help='weight coefficient for cross/random bind energy loss')

    args = parser.parse_args()
    print(args)
    model_save_dir = os.path.join(args.model_save_dir, f"lr{args.lr}_bs{args.batch_size}_nd{args.node_dim}_ed{args.edge_dim}"
                                                       f"_hd{args.hidden_dim}_nl{args.num_layers}_do{args.dropout}_ced{args.cut_edge_dis}")

    train_dataset = akscore2_dataset(args.data_dir, args.train_list_path,
                                     exclude_list_path=args.exclude_list_path,
                                     train_mode=True,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])
    valid_dataset = akscore2_dataset(args.data_dir, args.valid_list_path,
                                     exclude_list_path=args.exclude_list_path,
                                     train_mode=False,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers)
    val_loader = DataLoader(valid_dataset, batch_size=1, shuffle=False, num_workers=args.num_workers)

    print(f"train len: {len(train_loader)}, val len: {len(val_loader)},")

    loader = {
        'train_loader': train_loader,
        'val_loader': val_loader,
    }

    model = GATv2(args.node_dim, args.edge_dim, num_layers=args.num_layers, hidden_dim=args.hidden_dim,
                  dropout=args.dropout, cut_edge_dis=args.cut_edge_dis).to(DEVICE)

    criterion = {
        'mse': torch.nn.MSELoss(),
        'bce': torch.nn.BCEWithLogitsLoss(),
    }

    #     optimizer = torch.optim.RAdam(model.parameters(), lr=args.lr)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)

    ### resume model training
    resume_epoch = 1
    if os.path.isfile(args.resume_model_path):
        ch = torch.load(args.resume_model_path)
        model.load_state_dict(ch['model'])
        model.to(DEVICE)
        resume_epoch = ch['epoch'] + 1
        optimizer.load_state_dict(ch['optimizer'])
#         model_save_dir = model_save_dir + f"_resume_lr{args.lr}"
        for g in optimizer.param_groups:
            g['lr'] = args.lr
        print(f"model loaded: {args.resume_model_path}")

    if not os.path.exists(model_save_dir):
        os.makedirs(model_save_dir)


    for epoch in range(resume_epoch, args.max_epochs):
        print("=" * 120)
        print(f"Epoch {epoch} Start:")

        model, avg_epoch_train_loss = train_model_epoch(model, loader['train_loader'], criterion, optimizer, args.cr_loss_weight)
        time.sleep(2)

        print("\n\n")
        print_txt_train = f'Epoch: {epoch} \t train_total: {avg_epoch_train_loss["total"]:.4f} \t train_bind_n: {avg_epoch_train_loss["bind_native"]:.4f} \t ' \
                          f'train_bind_cr {avg_epoch_train_loss["bind_cross_random"]:.4f} \t train_bind_d: {avg_epoch_train_loss["bind_dock"]:.4f} \t '
        print("Train")
        print(print_txt_train)


        if epoch % 10 == 0:
            avg_epoch_val_loss = valid_model_epoch(model, loader['val_loader'], criterion, args.cr_loss_weight)
            time.sleep(2)
            print_txt_valid = f'Epoch: {epoch} \t valid_total: {avg_epoch_val_loss["total"]:.4f} \t valid_bind_n: {avg_epoch_val_loss["bind_native"]:.4f} \t ' \
                              f'valid_bind_cr {avg_epoch_val_loss["bind_cross_random"]:.4f} \t valid_bind_d: {avg_epoch_val_loss["bind_dock"]:.4f} \t ' \
                              f'valid_top1: {avg_epoch_val_loss["suc_top1"]} \t valid_top5: {avg_epoch_val_loss["suc_top5"]} \t '
            print("Valid")
            print(print_txt_valid)

            obj = {
                'epoch': epoch,
                'model': model.state_dict(),
                'optimizer': optimizer.state_dict(),
            }
            checkpoint_filename = f"checkpoint_{epoch}_tl_{avg_epoch_train_loss['total']:.3f}_vl_{avg_epoch_val_loss['total']:.3f}_" \
                                  f"vs1_{avg_epoch_val_loss['suc_top1']:.3f}_vs5_{avg_epoch_val_loss['suc_top5']:.3f}.pth"
            torch.save(obj, os.path.join(model_save_dir, checkpoint_filename))

            time.sleep(5)

            fconv = open(os.path.join(model_save_dir, 'train_val_log.tsv'), 'a')
            fconv.write(print_txt_train + print_txt_valid + '\n')
            fconv.close()






