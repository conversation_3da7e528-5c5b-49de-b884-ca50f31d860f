# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

本仓库包含 Akscore2 的训练和测试代码，这是一个用于预测蛋白质-配体相互作用的图神经网络模型。代码库似乎专注于药物发现中的虚拟筛选任务。

根据 `README.md`，有几个不同版本的模型：
- **v4**: Non-Dock
- **v7**: DockS
- **v8**: DockC

该研究的参考文献是："Hong, Y., Ha, J., Sim, J. et al. Accurate prediction of protein–ligand interactions by combining physical energy functions and graph-neural networks. J Cheminform 16, 121 (2024)."

## 代码结构

代码主要由 Python 脚本构成，使用 PyTorch 和 PyTorch Geometric 库。

- `train_akscore2_*.py`: 用于训练不同版本模型的脚本。这些脚本定义了模型架构（主要是 GATv2）、数据加载器和训练循环。
- `test_akscore2_*_dude_screen_pred.py`: 用于使用训练好的模型在 DUD-E 数据集上进行虚拟筛选预测的脚本。
- `slurm_*.sh`: 用于在 Slurm 集群上提交训练和测试作业的 Shell 脚本。这些文件包含了运行 Python 脚本所需的命令行参数。
- `ensemble_dude_results.py`: 可能是用于整合不同模型预测结果的脚本。
- `dude_cal_EF.py`: 可能是用于计算富集因子（Enrichment Factor, EF）的脚本，这是评估虚拟筛选性能的常用指标。

数据似乎是以 pickle 文件 (`.pkl` 或 `.pkl.gz`) 的形式存储的图对象。

## 如何运行

命令和路径是从 `slurm_train.sh` 和 `slurm_test.sh` 文件中推断出来的。实际路径可能需要根据本地环境进行调整。

### 模型训练

以下是如何训练 v4 模型的一个示例命令。可以根据 `slurm_train.sh` 中的其他命令调整参数以训练其他模型版本。

```bash
python train_akscore2_v4.py \
    --data_dir /path/to/your/data \
    --train_list_path /path/to/your/train_list.txt \
    --valid_list_path /path/to/your/valid_list.txt \
    --model_save_dir /path/to/save/models \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --batch_size 32 \
    --lr 0.0001
```

### 模型测试/筛选预测

以下是如何使用训练好的 v4 模型进行筛选预测的示例。

```bash
python test_akscore2_v4_dude_platform_screen_pred.py \
    --screen_dir /path/to/dude/dataset \
    --target_name <target_protein_name> \
    --model_path /path/to/your/trained_model.pth \
    --result_save_dir /path/to/save/results \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5
```

## 训练脚本详解 (供深度学习初学者参考)

这三个脚本 (`train_akscore2_v4.py`, `train_akscore2_v7_bind_rmsd.py`, `train_akscore2_v8.py`) 代表了 `Akscore2` 模型开发过程中的三个不同阶段或版本。它们都基于图神经网络（GNN）来预测蛋白质和配体（小分子）之间的相互作用，但它们在模型架构、训练目标和数据处理方式上有所不同。

### 核心概念解析 (深度学习入门)

对于刚接触深度学习的同事，理解以下几个概念是关键：

1.  **PyTorch & PyTorch Geometric (PyG)**:
    *   `torch`: 是一个核心的深度学习框架，提供了构建神经网络所需的基本工具，例如张量（`torch.Tensor`，可以看作是能利用GPU加速的多维数组）和自动求导机制。
    *   `torch.nn.Module`: 是所有神经网络模型的基类。我们通过继承这个类来定义自己的模型架构。它的 `forward` 方法定义了数据如何通过网络层进行计算。
    *   `torch_geometric`: 是一个专门为图神经网络（GNN）构建的PyTorch扩展库。它提供了如图卷积层（如 `GATv2Conv`）、图数据结构（`Data`）和高效加载图数据的工具（`DataLoader`）等。

2.  **图神经网络 (GNN) 基础**:
    *   **什么是图？**: 在这个项目中，一个“图”代表一个蛋白质-配体复合物。图由“节点”（`nodes`）和“边”（`edges`）组成。
        *   **节点 (Nodes)**: 代表原子。每个节点都有一系列特征（`node features`），比如原子类型、电荷等。
        *   **边 (Edges)**: 代表原子之间的关系，比如化学键或空间上的邻近关系。每条边也可以有特征（`edge features`），比如键的类型或原子间的距离。
    *   **GNN 如何工作**: GNN 的核心思想是通过“消息传递”来更新节点的表示。在每一层，每个节点会聚合其邻居节点和连接边的信息，然后用这些聚合来的信息来更新自身的特征表示。经过多层堆叠，每个节点的最终表示就包含了其在图中的局部和更广泛的邻域信息。

3.  **模型训练流程**:
    *   **Dataset & DataLoader**:
        *   `Dataset` 类 (如 `akscore2_dataset`) 负责加载和预处理数据。它的 `__getitem__` 方法定义了如何获取单个数据样本。
        *   `DataLoader` 则将 `Dataset` 包装起来，自动处理数据的批处理（batching）、打乱（shuffling）和并行加载，为模型训练提供方便的数据流。
    *   **训练循环 (Training Loop)**:
        1.  **前向传播 (Forward Pass)**: 将一批数据输入模型，执行 `forward` 方法，得到模型的预测输出。
        2.  **计算损失 (Loss Calculation)**: 将模型的预测结果与真实标签（ground truth）进行比较，计算出一个“损失值”。损失值越小，表示模型预测得越准。这里用了 `MSELoss`（均方误差，用于回归任务）和 `BCEWithLogitsLoss`（带 Sigmoid 的二元交叉熵，用于二分类任务）。
        3.  **反向传播 (Backward Pass)**: PyTorch 会自动计算损失相对于模型所有参数的梯度（导数）。这个梯度指明了参数应该如何调整才能使损失变小。
        4.  **更新参数 (Optimizer Step)**: 优化器（如 `Adam`）根据计算出的梯度来微调模型的参数。
    *   这个过程会重复很多轮（`epoch`），直到模型的性能不再提升。

---

### 各版本训练脚本详解

下面我们来深入分析 `v4`, `v7`, `v8` 三个版本的具体实现和区别。

#### 1. `train_akscore2_v4.py` (Non-Dock 版本)

这个版本似乎是一个早期的、相对简单的模型。

*   **模型架构 (`GATv2`)**:
    *   **“先合后分”的分离式处理**: 这个模型的核心特点是，虽然输入的数据文件是蛋白质-配体复合物的完整图，但在数据加载阶段会通过 `graph_modification` 函数动态地将其拆分为独立的 `protein_graph` 和 `ligand_graph`。
    *   **独立的GNN通道**: 模型有两套独立的GNN层：`protein_gnn_layers` 和 `ligand_gnn_layers`。值得注意的是，这两套GNN的层数并不同，配体GNN的层数是蛋白质GNN的一半加一 (`num_layers//2+1`)。这可能是基于蛋白质结构更复杂，需要更深的网络来提取特征的假设。
    *   **信息聚合与预测**: 两个独立的图分别通过各自的GNN通道后，使用 `global_mean_pool` 将节点特征聚合成代表全局的向量。最后，这两个向量被拼接（`concat`）起来，输入到一个全连接网络中进行最终的二分类预测。

*   **训练目标**:
    *   该模型只预测一个任务：判断一个配体构象（pose）的好坏。
    *   它将这个问题简化为一个**二分类任务**。`rmsd_cutoff` 参数（例如 2Å）被用作阈值。如果一个构象的真实RMSD（与晶体结构相比的偏差）低于这个阈值，它被认为是“好”的（标签为1）；否则认为是“坏”的（标签为0）。
    *   损失函数 `BCEWithLogitsLoss` 就是为此类二分类任务设计的。

*   **数据处理与损失函数 (`akscore2_dataset`)**:
    *   **数据采样策略**: 在每个训练周期（epoch）开始时，脚本会调用 `make_dataset` 方法重新构建训练集。策略如下：
        1.  包含所有 **天然构象** (native, 类型 `_0_`)。
        2.  随机采样2倍天然构象数量的 **交叉构象** (cross decoys, 类型 `_2_`)。
        3.  随机采样2倍天然构象数量的 **随机构象** (random decoys, 类型 `_3_`)。
        4.  **对接构象** (docked decoys, 类型 `_1_`) 的采样代码被注释掉了，因此 **完全不参与训练**。
        *   最终，训练集的数据来源比例固定为 **1 (native) : 2 (cross) : 2 (random)**。所有数据被混合并完全打乱后，再由 `DataLoader` 生成批次。
    *   **统一的二分类损失**: 脚本将任务简化为一个二分类问题。对于所有来源的样本，都会根据其RMSD值是否超过 `rmsd_cutoff` (默认为 2Å) 来生成一个“好”(1) 或“坏”(0) 的标签。损失函数 `BCEWithLogitsLoss` 对所有样本一视同仁，统一计算预测值与这个二元标签之间的损失。模型的目标是学会普适性的区分好坏构象的特征，而不是针对特定数据来源进行优化。

*   **小结**: v4 是一个**分离式、单任务的分类模型**。它在数据加载时将复合物拆分为独立的蛋白质和配体图，并使用不同深度的GNN分别处理。通过精心设计的数据采样比例（排除对接构象，增加负样本）和一个简化的二分类损失函数来训练模型，使其能够有效地区分出高质量的对接姿态。

#### 2. `train_akscore2_v7_bind_rmsd.py` (DockS 版本)

这个版本比 v4 复杂得多，引入了多任务学习和更复杂的损失函数。`README.md` 称之为 "DockS"。

*   **模型架构 (`GATv2`)**:
    *   **一体化处理**: 与v4不同，v7只有一个GNN层集合 `protein_ligand_gnn_layers`。它直接在完整的蛋白质-配体复合物图上进行消息传递。这允许模型直接学习原子间的相互作用。
    *   **多任务输出**: GNN处理后的全局图特征 `protein_ligand_x_dock` 被同时输入到两个不同的“头”网络中：
        1.  `graph_dec_bind`: 预测结合亲和力（binding affinity），这是一个**回归任务**。
        2.  `graph_dec_rmsd`: 预测RMSD值，也是一个**回归任务**。
    *   这种架构让模型能够同时学习“这个配体能结合多紧？”（亲和力）和“这个对接姿势有多准？”（RMSD）。

*   **训练目标与复合损失函数**:
    *   这是一个**多任务回归模型**，其总损失 `total_loss` 是三个针对不同数据来源的子损失的加和，构成了一个精巧的多任务学习框架：
        1.  `loss_bind_native_mse`: **仅针对天然构象 (native)**，使用均方误差(MSE)损失来精确预测其结合亲和力。
        2.  `loss_bind_cross_random`: **仅针对交叉(cross)和随机构象(random)**，采用一种类似铰链损失的惩罚机制。它不要求模型预测精确的亲和力，而是惩罚那些被错误地预测为亲和力过强（预测值 < -5）的样本。这教会了模型将“垃圾”分子识别出来并给予较差的评分。
        3.  `loss_rmsd`: **仅针对天然构象(native)和对接构象(dock)**，使用均方误差(MSE)损失来精确预测构象的RMSD。这使得模型能够学习评估一个对接姿势的准确性。
    *   通过这个复合损失函数，模型被训练以同时回答三个问题：“这个分子能结合多好？”（亲和力回归）、“这个对接姿势有多准？”（RMSD回归）以及“哪些是无关的分子？”（干扰项分类）。

*   **数据处理 (`akscore2_dataset`)**:
    *   **复杂的数据采样**: 为了构造一个富有挑战性的训练环境，`get` 方法为 `DataLoader` 准备的每个“样本集合”都包含了固定的数据比例。它会包含 **1个天然构象(native)**，并从整个数据集中随机采样 **4个对接构象(dock)**、**2个交叉构象(cross)** 和 **2个随机构象(random)**。
    *   **数据增强**: 对结合亲和力标签进行随机扰动（`graph.bind = graph.bind + np.random.uniform(...)`），这是一种常见的数据增强技术，可以提高模型的泛化能力。

*   **小结**: v7 是一个**一体化、多任务的回归模型**。它直接在复合物图上学习，通过复杂的数据采样策略和针对不同数据来源的复合损失函数，让模型同时具备了预测结合亲和力、评估对接姿势质量以及区分干扰项的能力，这对于虚拟筛选任务至关重要。

#### 3. `train_akscore2_v8.py` (DockC 版本)

v8 在 v7 的基础上进行了微调和简化，似乎是想更专注于结合亲和力的预测。`README.md` 称之为 "DockC"。

*   **模型架构 (`GATv2`)**:
    *   **回归单任务**: 模型架构与v7类似（一体化处理），但它**移除了RMSD预测头** (`graph_dec_rmsd`)。现在模型只专注于预测结合亲和力 (`bind_logits`)。

*   **训练目标与复合损失函数**:
    *   目标是**只预测结合亲和力**，但通过一个精巧的复合损失函数来实现，该函数由三个针对不同数据来源的子损失构成：
        1.  `loss_bind_native_mse`: **仅针对天然构象 (native)**，使用均方误差(MSE)来确保模型对正确的结合模式预测得尽可能精确。
        2.  `loss_bind_dock_mse`: **仅针对对接构象 (dock)**，同样使用MSE。关键在于，这些构象的结合亲和力标签已在预处理中被增强 (`label = original_bind + RMSD`)。因此，这个损失函数在优化亲和力预测的同时，也隐式地让模型学习姿势的准确性。
        3.  `loss_bind_cross_random`: **针对交叉(cross)和随机构象(random)**，采用铰链损失(Hinge-like Loss)机制。它不要求预测精确值，而是惩罚那些被错误地预测为亲和力过强（预测值 < -5）的“垃圾”分子。
    *   最终的 `total_loss` 是这三者之和，它让模型在同一框架下学会了三种关键能力：对好样本的精确回归、对中等样本的质量感知回归、以及对差样本的有效排斥。

*   **数据处理与标签增强 (`akscore2_dataset`)**:
    *   **数据采样策略**: 采用与v7类似的复杂数据采样，为每个 **天然构象(native)** 匹配一个包含 **4个对接构象(dock)**、**2个交叉构象(cross)** 和 **2个随机构象(random)** 的“样本集合”，以构造富有挑战性的训练环境。
    *   **核心创新点 (标签增强)**: 在 `graph_rmsd_bindaff_aug` 函数中，对**对接构象** (docked decoys) 的处理是此版本的关键。
        ```python
        if name_splited[-2] == '1': ###if decoy dock
            graph.bind = graph.bind + graph.y
        ```
        `graph.y` 是该构象的RMSD值。此操作将姿势的好坏（RMSD）信息直接编码到了结合亲和力的训练目标中。一个RMSD越大的（越不准的）构象，其结合亲和力伪标签就越差（因为结合能是负数，加上一个正的RMSD值会使其变得“不那么好”）。这迫使模型在预测结合能的同时，隐式地学会了评估姿势的质量。

*   **小结**: v8 是一个**一体化、单任务（但目标被增强）的回归模型**。它通过巧妙的**标签增强**技术，将姿势质量（RMSD）信息融入到单一的结合亲和力预测任务中。结合其复合损失函数，模型被训练成一个能够对不同质量的配体构象进行精确、有区别的打分器，非常适合虚拟筛选任务。

### 总结与对比

| 特性 | `train_akscore2_v4.py` (Non-Dock) | `train_akscore2_v7_bind_rmsd.py` (DockS) | `train_akscore2_v8.py` (DockC) |
| :--- | :--- | :--- | :--- |
| **模型范式** | 分离式 GNN (蛋白/配体分开) | 一体化 GNN (复合物整体) | 一体化 GNN (复合物整体) |
| **学习任务** | 单任务：姿势分类 (好/坏) | 多任务：结合能回归 + RMSD回归 | 单任务：结合能回归 |
| **损失函数** | 二元交叉熵损失 (BCE) | 复合损失 (MSE-native, Hinge-decoys, MSE-rmsd) | 复合损失 (MSE-native, Hinge-decoys, MSE-dock) |
| **核心创新点** | 简单的基线模型 | 同时预测结合能和姿势质量 | **将RMSD信息编码进结合能的训练标签中** |
| **数据处理** | 简单的训练集列表 | 复杂的数据采样策略，引入多种干扰项 | 与v7类似，但有关键的标签增强步骤 |

---

## 训练脚本输入要求详解

基于对三个训练脚本的分析，以下是运行这些脚本所需的输入文件和参数的详细说明：

### 必需的输入文件

#### 1. 数据目录结构 (`--data_dir`)

数据目录应该按照以下层次结构组织：

```
data_dir/
├── pdb_id_1/
│   ├── complex_name_0_0.pkl      # 天然构象 (native)
│   ├── complex_name_1_X.pkl      # 对接构象 (docked poses)
│   ├── complex_name_2_X.pkl      # 交叉构象 (cross decoys)
│   └── complex_name_3_X.pkl      # 随机构象 (random decoys)
├── pdb_id_2/
│   └── ...
└── pdb_id_N/
    └── ...
```

**文件命名规则**：
- 文件名格式：`{complex_name}_{type}_{index}.pkl`
- `type` 字段含义：
  - `0`: 天然构象 (native pose) - 来自晶体结构的真实结合模式
  - `1`: 对接构象 (docked pose) - 通过分子对接生成的构象
  - `2`: 交叉构象 (cross decoy) - 来自其他蛋白质的配体构象
  - `3`: 随机构象 (random decoy) - 随机生成的配体构象

**数据文件内容**：
每个 `.pkl` 文件包含一个 PyTorch Geometric `Data` 对象，具有以下属性：
- `x`: 节点特征矩阵 (原子特征)
- `edge_index`: 边索引矩阵 (原子间连接)
- `edge_attr`: 边特征矩阵 (键特征、距离等)
- `y`: RMSD 值 (相对于天然构象的偏差)
- `bind`: 结合亲和力值 (-logKd/Ki)
- `name`: 复合物名称标识符

#### 2. PDB ID 列表文件

**训练列表** (`--train_list_path`):
```
1abc
2def
3ghi
...
```

**验证列表** (`--valid_list_path`):
```
4jkl
5mno
6pqr
...
```

**测试列表** (`--test_list_path`, 可选):
```
7stu
8vwx
9yz1
...
```

**排除列表** (`--exclude_list_path`, 可选):
```
bad_pdb_1
bad_pdb_2
...
```

### 主要命令行参数

#### 通用参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--data_dir` | str | 必需 | 包含训练数据的根目录 |
| `--train_list_path` | str | 必需 | 训练集PDB ID列表文件路径 |
| `--valid_list_path` | str | 必需 | 验证集PDB ID列表文件路径 |
| `--test_list_path` | str | 可选 | 测试集PDB ID列表文件路径 |
| `--exclude_list_path` | str | 可选 | 需要排除的PDB ID列表文件路径 |
| `--model_save_dir` | str | 必需 | 模型保存目录 |
| `--resume_model_path` | str | 可选 | 恢复训练的模型检查点路径 |

#### 模型架构参数

| 参数 | v4默认值 | v7默认值 | v8默认值 | 说明 |
|------|----------|----------|----------|------|
| `--node_dim` | 72 | 73 | 73 | 节点特征维度 |
| `--edge_dim` | 12 | 24 | 24 | 边特征维度 |
| `--hidden_dim` | 256 | 128 | 128 | 隐藏层维度 |
| `--num_layers` | 5 | 5 | 5 | GNN层数 |
| `--dropout` | 0.25 | 0.25 | 0.25 | Dropout比率 |
| `--cut_edge_dis` | N/A | 8 | 8 | 边距离截断 (4/6/8 Å) |

#### 训练参数

| 参数 | v4默认值 | v7默认值 | v8默认值 | 说明 |
|------|----------|----------|----------|------|
| `--lr` | 0.001 | 0.0001 | 0.0001 | 学习率 |
| `--batch_size` | 32 | 4 | 4 | 批大小 |
| `--num_workers` | 8 | 8 | 8 | 数据加载器工作进程数 |

#### 数据处理参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--rmsd_cutoff` | 2 | RMSD阈值 (Å)，用于二分类 |
| `--bind_aff_aug_upper` | 1 | 结合亲和力增强上限 |
| `--bind_aff_aug_lower` | -1 | 结合亲和力增强下限 |
| `--bind_aff_rej_upper` | 0 | 结合亲和力拒绝上限 |
| `--bind_aff_rej_lower` | -5 | 结合亲和力拒绝下限 |

### 版本特定的输入要求

#### v4 (Non-Dock) 特殊要求：
- 节点特征维度较小 (72 vs 73)
- 边特征维度较小 (12 vs 24)
- 批大小较大 (32 vs 4)
- 主要用于二分类任务，关注RMSD阈值设置

#### v7 (DockS) 特殊要求：
- 需要完整的多类型构象数据 (native, dock, cross, random)
- 支持边距离截断参数 `--cut_edge_dis`
- 多任务学习，同时预测结合亲和力和RMSD

#### v8 (DockC) 特殊要求：
- 与v7类似的数据要求
- 特殊的标签增强：对接构象的结合亲和力会加上RMSD值
- 专注于结合亲和力预测，移除了RMSD预测头

### 实际运行示例

基于 `slurm_train.sh` 中的实际命令，以下是三个版本的典型运行示例：

#### v4 训练示例：
```bash
python train_akscore2_v4.py \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --num_workers 8 \
    --batch_size 32 \
    --model_save_dir /path/to/model_save_dir \
    --data_dir /path/to/data_5A_redock_cross_all \
    --train_list_path /path/to/train_list_5A.txt \
    --valid_list_path /path/to/valid_list_5A.txt \
    --lr 0.0001
```

#### v7 训练示例：
```bash
python train_akscore2_v7_bind_rmsd.py \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --cut_edge_dis 8 \
    --num_workers 8 \
    --batch_size 4 \
    --model_save_dir /path/to/model_save_dir \
    --data_dir /path/to/data_5A_redock_cross_all \
    --train_list_path /path/to/train_list_5A.txt \
    --valid_list_path /path/to/valid_list_5A.txt \
    --lr 0.0001
```

#### v8 训练示例：
```bash
python train_akscore2_v8.py \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --cut_edge_dis 8 \
    --num_workers 8 \
    --batch_size 4 \
    --model_save_dir /path/to/model_save_dir \
    --data_dir /path/to/data_5A_redock_cross_all \
    --train_list_path /path/to/train_list_5A.txt \
    --valid_list_path /path/to/valid_list_5A.txt \
    --lr 0.0001
```

### 输出文件

训练过程会在 `model_save_dir` 中生成以下文件：
- `checkpoint_*.pth`: 模型检查点文件
- `train_val_log.csv` (v4) 或 `train_val_log.tsv` (v7/v8): 训练日志文件
- 模型保存目录会根据参数自动命名，如：`lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8`
