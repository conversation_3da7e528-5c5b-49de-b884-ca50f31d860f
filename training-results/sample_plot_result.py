import numpy as np
import pandas as pd
import os
import time
import matplotlib.pyplot as plt
import sys

def plot_regression_metrics_from_log(folder_name):
    """从日志文件读取并绘制回归任务的损失、MAE变化图和学习率变化图"""
    # 构建日志文件路径
    log_file_path = os.path.join(folder_name, 'logs', 'training_log_regression.txt')
    
    # 读取日志文件
    with open(log_file_path, 'r') as f:
        lines = f.readlines()
    
    # 跳过标题行
    data_lines = lines[1:]
    
    # 解析数据
    epochs = []
    train_loss = []
    test_loss = []
    train_mae = []
    train_rmse = []
    test_mae = []
    test_rmse = []
    learning_rates = []
    
    for line in data_lines:
        parts = line.strip().split('|')
        if len(parts) >= 9:  # 确保包含学习率列
            epochs.append(int(parts[0].strip()))
            train_loss.append(float(parts[2].strip()) if parts[2].strip() != 'N/A' else float('nan'))
            test_loss.append(float(parts[3].strip()) if parts[3].strip() != 'N/A' else float('nan'))
            train_mae.append(float(parts[4].strip()) if parts[4].strip() != 'N/A' else float('nan'))
            train_rmse.append(float(parts[5].strip()) if parts[5].strip() != 'N/A' else float('nan'))
            test_mae.append(float(parts[6].strip()) if parts[6].strip() != 'N/A' else float('nan'))
            test_rmse.append(float(parts[7].strip()) if parts[7].strip() != 'N/A' else float('nan'))
            learning_rates.append(float(parts[8].strip()) if parts[8].strip() != 'N/A' else float('nan'))
    
    # 找到test MAE的最小值及其对应的epoch
    min_test_mae = min(test_mae)
    min_test_mae_epoch = epochs[test_mae.index(min_test_mae)]
    
    # 创建三个子图，调整整体高度为10
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(5, 10), height_ratios=[1, 0.6, 1])
    
    # 添加总标题
    fig.suptitle(folder_name, fontsize=12)
    
    # 上方图：损失
    color = 'tab:grey'
    ax1.set_xlabel('Epochs')
    ax1.set_ylabel('Loss', color=color)
    ax1.plot(epochs, train_loss, 'o-', color='tab:blue', label='Train Loss', markersize=6)
    ax1.plot(epochs, test_loss, 's-', color='tab:orange', label='Test Loss', markersize=6)
    ax1.axvline(x=min_test_mae_epoch, color='gray', linestyle='--', alpha=0.5)  # 添加垂直虚线
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper right')
    ax1.set_title('Loss')
    ax1.set_xlim(1, max(epochs))  # 设置x轴从1开始
    
    # 中间图：学习率
    color = 'tab:brown'
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('Learning Rate', color=color)
    ax2.plot(epochs, learning_rates, 'D-', color=color, label='Learning Rate')
    ax2.tick_params(axis='y', labelcolor=color)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper right')
    ax2.set_title('Learning Rate')
    ax2.set_xlim(1, max(epochs))  # 设置x轴从1开始
    
    # 下方图：MAE和RMSE指标
    color = 'tab:grey'
    ax3.set_xlabel('Epochs')
    ax3.set_ylabel('Metrics', color=color)
    ax3.plot(epochs, train_mae, 's-', color='tab:blue', label='Train MAE')
    ax3.plot(epochs, train_rmse, 'v-', color='tab:cyan', label='Train RMSE')
    ax3.plot(epochs, test_mae, '^-', color='tab:orange', label='Test MAE')
    ax3.plot(epochs, test_rmse, 'p-', color='tab:red', label='Test RMSE')
    # 添加最小test MAE的水平线
    ax3.axhline(y=min_test_mae, color='gray', linestyle=':', alpha=0.5)
    # 在最小值点下方添加标注
    ax3.annotate(f'{min_test_mae:.4f}', 
                xy=(min_test_mae_epoch, min_test_mae),
                xytext=(min_test_mae_epoch, min_test_mae - 0.05),
                ha='center', va='top')
    ax3.tick_params(axis='y', labelcolor=color)
    ax3.grid(True, alpha=0.3)
    #ax3.set_ylim(0, 5)
    ax3.legend(loc='upper right')
    ax3.set_title('Training metrics')
    ax3.set_xlim(1, max(epochs))  # 设置x轴从1开始
    
    plt.tight_layout()
    plt.savefig(f'{folder_name}.png')
    print(f"\n指标图已保存至 '{folder_name}.png'")
    #plt.show()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python plot_result.py <folder_name>")
        sys.exit(1)
    plot_regression_metrics_from_log(sys.argv[1])

