import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def plot_metrics(csv_file='train_val_log.csv', output_file='training_metrics.png'):
    """
    从 CSV 日志文件中读取训练指标并生成可视化图表。

    图表包括:
    1. 训练/验证损失 vs. Epoch
    2. 训练/验证 AUC vs. Epoch (左轴) 和 Top1/Top5 成功率 vs. Epoch (右轴)
    """
    # 读取CSV文件
    try:
        # 使用 pandas 读取数据，并清理列名中的潜在空格
        df = pd.read_csv(csv_file)
        df.columns = df.columns.str.strip()
        # 移除任何可能因尾随逗号产生的空列
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
    except FileNotFoundError:
        print(f"错误: 文件 '{csv_file}' 未找到。")
        return
    except Exception as e:
        print(f"读取或处理CSV时出错: {e}")
        return

    # --- 创建图表 ---
    # 创建一个2行1列的子图布局，设置图像大小
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(5, 8))
    fig.suptitle('NonDock w./ coreset', fontsize=16)

    # --- 图1: Loss vs. Epoch ---
    ax1.plot(df['epoch'], df['train_rmsd_loss'], 'o-', color='tab:blue', label='Training Loss', markersize=4)
    ax1.plot(df['epoch'], df['valid_rmsd_loss'], 's-', color='lightseagreen', label='Validation Loss', markersize=4)
    ax1.set_title('Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, linestyle='--', alpha=0.6)
    # 设置x轴范围以更好地显示数据
    ax1.set_xlim(left=df['epoch'].min())

    # --- 图2: AUC and Success Rate vs. Epoch (双y轴) ---
    color_auc = 'tab:blue'
    color_suc = 'tab:orange'

    # 左轴 (AUC)
    ax2.plot(df['epoch'], df['train_auc'], 'o-', color=color_auc, label='Train AUC', markersize=4)
    ax2.plot(df['epoch'], df['valid_auc'], 's-', color='lightseagreen', label='Valid AUC', markersize=4)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('AUC', color=color_auc)
    ax2.tick_params(axis='y', labelcolor=color_auc)
    ax2.set_ylim(bottom=0.5, top=1.0) # 设置合理的AUC范围
    ax2.set_xlim(left=df['epoch'].min())

    # 创建共享x轴的右侧y轴
    ax_right = ax2.twinx()

    # 右轴 (Success Rate)
    ax_right.plot(df['epoch'], df['valid_suc_top1'] * 100, '^-', color=color_suc, label='Valid Success Top1 (%)', markersize=4)
    ax_right.plot(df['epoch'], df['valid_suc_top5'] * 100, 'd-', color='salmon', label='Valid Success Top5 (%)', markersize=4)
    ax_right.set_ylabel('Success Rate (%)', color=color_suc)
    ax_right.tick_params(axis='y', labelcolor=color_suc)
    ax_right.set_ylim(0, 100)

    ax2.set_title('AUC & Validation Success Rate')

    # 合并双轴的图例
    lines, labels = ax2.get_legend_handles_labels()
    lines_right, labels_right = ax_right.get_legend_handles_labels()
    ax_right.legend(lines + lines_right, labels + labels_right, loc='best')

    ax2.grid(True, linestyle='--', alpha=0.6)

    # 调整整体布局以避免标题重叠
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    
    # 保存图像
    plt.savefig(output_file, dpi=300)
    plt.show()

    print(f"图表已成功保存至 '{output_file}'")

if __name__ == '__main__':
    # 你可以直接运行此脚本，它会读取同目录下的 'train_val_log.csv'
    # 并生成 'training_metrics.png'
    plot_metrics()
