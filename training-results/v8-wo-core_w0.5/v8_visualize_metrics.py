import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re

def parse_tsv_log(file_path='train_val_log.tsv'):
    """
    解析特殊格式的TSV日志文件
    """
    data = []

    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            # 修复bind_cr格式问题：在bind_cr后添加冒号
            line = re.sub(r'(train_bind_cr|valid_bind_cr)\s+', r'\1: ', line)

            # 使用正则表达式提取所有的键值对
            pattern = r'(\w+):\s*([\d.-]+)'  # 标准格式: key: value
            matches = re.findall(pattern, line)

            # 将匹配结果转换为字典
            row_data = {}
            for key, value in matches:
                try:
                    row_data[key] = float(value)
                except ValueError:
                    row_data[key] = value

            if row_data:  # 只添加非空的行数据
                data.append(row_data)

    # 转换为DataFrame
    df = pd.DataFrame(data)
    return df

def plot_training_metrics(tsv_file='train_val_log.tsv', output_file='training_metrics.png'):
    """
    从TSV日志文件中读取训练指标并生成可视化图表

    图表包括:
    1. train_total/valid_total vs. epoch
    2. train_bind_n/valid_bind_n/train_bind_d/valid_bind_d vs. epoch
    3. train_bind_cr/valid_bind_cr vs. epoch
    4. valid_top1/valid_top5 vs. epoch (value需要乘以100，range: 0-100)
    """

    # 读取并解析TSV文件
    try:
        df = parse_tsv_log(tsv_file)
        print(f"成功读取数据，共 {len(df)} 个epoch")
        print("可用的列:", df.columns.tolist())
    except FileNotFoundError:
        print(f"错误: 文件 '{tsv_file}' 未找到。")
        return
    except Exception as e:
        print(f"读取或处理TSV时出错: {e}")
        return

    # 检查必要的列是否存在
    required_cols = ['Epoch', 'train_total', 'valid_total', 'train_bind_n', 'valid_bind_n',
                     'train_bind_d', 'valid_bind_d', 'train_bind_cr', 'valid_bind_cr',
                     'valid_top1', 'valid_top5']

    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"警告: 缺少以下列: {missing_cols}")

    # 创建图表 - 4个子图
    fig, axes = plt.subplots(4, 1, figsize=(4.5, 9), sharex=True)
    fig.suptitle('V8 w/o coreset', fontsize=12, y=1.0)
    
    # 定义颜色和标记样式
    train_color = 'tab:blue'
    valid_color = 'lightseagreen'

    # 图1: train_total/valid_total vs. epoch
    if 'train_total' in df.columns and 'valid_total' in df.columns:
        axes[0].plot(df['Epoch'], df['train_total'], 'o-', color=train_color,
                    label='Train Total', markersize=5, linewidth=2)
        axes[0].plot(df['Epoch'], df['valid_total'], 's-', color=valid_color,
                    label='Valid. Total', markersize=5, linewidth=2)
        axes[0].set_title('Total Loss', fontsize=12, fontweight='bold')
        axes[0].set_ylabel('Loss', fontsize=11)
        axes[0].legend(loc='best', fontsize=10)
        axes[0].grid(True, linestyle='--', alpha=0.6)

    # 图2: train_bind_n/valid_bind_n/train_bind_d/valid_bind_d vs. epoch
    bind_nd_cols = ['train_bind_n', 'valid_bind_n', 'train_bind_d', 'valid_bind_d']
    available_bind_nd_cols = [col for col in bind_nd_cols if col in df.columns]

    if len(available_bind_nd_cols) >= 2:
        # Train curves with different markers
        if 'train_bind_n' in df.columns:
            axes[1].plot(df['Epoch'], df['train_bind_n'], 'o-', color=train_color,
                        label='Train Native', markersize=5, linewidth=2)
        if 'train_bind_d' in df.columns:
            axes[1].plot(df['Epoch'], df['train_bind_d'], '^-', color=train_color,
                        label='Train Docked', markersize=5, linewidth=2, alpha=0.8)

        # Valid. curves with different markers
        if 'valid_bind_n' in df.columns:
            axes[1].plot(df['Epoch'], df['valid_bind_n'], 's-', color=valid_color,
                        label='Valid. Native', markersize=5, linewidth=2)
        if 'valid_bind_d' in df.columns:
            axes[1].plot(df['Epoch'], df['valid_bind_d'], 'v-', color=valid_color,
                        label='Valid. Docked', markersize=5, linewidth=2, alpha=0.8)

        axes[1].set_title('Bind Loss (Native/Docked)', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('Loss', fontsize=11)
        axes[1].legend(loc='best', fontsize=10)
        axes[1].grid(True, linestyle='--', alpha=0.6)

    # 图3: train_bind_cr/valid_bind_cr vs. epoch
    if 'train_bind_cr' in df.columns and 'valid_bind_cr' in df.columns:
        axes[2].plot(df['Epoch'], df['train_bind_cr'], 'v-', color=train_color,
                    label='Train C/R', markersize=5, linewidth=2)
        axes[2].plot(df['Epoch'], df['valid_bind_cr'], 'p-', color=valid_color,
                    label='Valid. C/R', markersize=5, linewidth=2)
        axes[2].set_title('Bind Loss (Cross/Random)', fontsize=12, fontweight='bold')
        axes[2].set_ylabel('Loss', fontsize=11)
        axes[2].legend(loc='best', fontsize=10)
        axes[2].grid(True, linestyle='--', alpha=0.6)

    # 图4: valid_top1/valid_top5 vs. epoch (value需要乘以100，range: 0-100)
    if 'valid_top1' in df.columns and 'valid_top5' in df.columns:
        axes[3].plot(df['Epoch'], df['valid_top1'] * 100, '^-', color=valid_color,
                    label='Valid. Top3 (%)', markersize=5, linewidth=2)
        axes[3].plot(df['Epoch'], df['valid_top5'] * 100, 'D-', color='salmon',
                    label='Valid. Top10 (%)', markersize=5, linewidth=2)
        axes[3].set_title('Success Rate', fontsize=12, fontweight='bold')
        axes[3].set_ylabel('Success Rate (%)', fontsize=11)
        axes[3].set_ylim(0, 50)
        axes[3].legend(loc='best', fontsize=10)
        axes[3].grid(True, linestyle='--', alpha=0.6)

    # 设置共享的x轴标签
    axes[3].set_xlabel('Epoch', fontsize=11)

    # 设置x轴范围
    if 'Epoch' in df.columns:
        for ax in axes:
            ax.set_xlim(left=df['Epoch'].min())

    # 调整子图间距 - 尽量缩小间距
    plt.tight_layout(rect=[0, 0.02, 1, 0.96])
    plt.subplots_adjust(hspace=0.15, top=0.94)  # 进一步减小子图间距
    
    # 保存图像
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"图表已成功保存至 '{output_file}'")

if __name__ == '__main__':
    # 运行脚本生成可视化图表
    plot_training_metrics()
