
import re
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def parse_log_file(filepath):
    """
    Parses the custom-formatted log file into a list of dictionaries.
    """
    data = []
    with open(filepath, 'r') as f:
        for line in f:
            # Ensure bind_cr keys include colon
            line = re.sub(r'(\b[\w_]+_cr)\s+([-\d.]+)', r'\1: \2', line)
            # This regex finds all key-value pairs, handling the repeated 'Epoch' key correctly.
            # It looks for a key (word), followed by a colon, optional spaces, and a number (float or int).
            # The pattern is non-greedy to handle the two parts of the line separately.
            
            # Split the line into train and valid parts based on the second 'Epoch'
            parts = line.strip().split('Epoch:')[1:]
            if len(parts) < 2:
                continue
                
            train_part = "Epoch:" + parts[0].replace("train_bind_cr", "train_bind_cr:")
            valid_part = "Epoch:" + parts[1].replace("valid_bind_cr", "valid_bind_cr:")
            
            # Find all key-value pairs in each part
            train_metrics = re.findall(r'([\w_]+)[:\s]+([-\d.]+)', train_part)
            valid_metrics = re.findall(r'([\w_]+)[:\s]+([-\d.]+)', valid_part)

            # Combine and convert to a dictionary
            entry = {}
            # The epoch is the same, so we just take it from the training part
            entry['epoch'] = int(train_metrics[0][1])

            # Process metrics, skipping the epoch key
            for key, value in train_metrics[1:]:
                entry[key] = float(value)
            for key, value in valid_metrics[1:]:
                entry[key] = float(value)

            data.append(entry)
            
    return pd.DataFrame(data)

def plot_metrics(df):
    """
    Generates and saves the plots for training and validation metrics.
    """
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, axes = plt.subplots(3, 1, figsize=(4, 8), sharex=True)
    fig.suptitle('Training Metrics', fontsize=16)

    # --- Plot 1: Total Loss ---
    ax1 = axes[0]
    ax1.plot(df['epoch'], df['train_total'], 'o-', label='train_total', color='tab:blue', markersize=4)
    ax1.plot(df['epoch'], df['valid_total'], 's-', label='valid_total', color='tab:orange', markersize=4)
    ax1.set_ylabel('Total Loss')
    ax1.set_title('Total Loss vs. Epoch')
    ax1.legend(loc='best')
    ax1.grid(True, which='both', linestyle='--', alpha=0.6)
    ax1.set_xlim(left=df['epoch'].min())

    # --- Plot 2: Bind/RMSD Losses (Dual Axis) ---
    ax2 = axes[1]
    # Left Axis (Bind losses)
    # Train colors: blues
    ax2.plot(df['epoch'], df['train_bind_n'], 'o-', label='train_bind_n', color='tab:blue', markersize=4)
    ax2.plot(df['epoch'], df['train_bind_cr'], 'o-', label='train_bind_cr', color='lightseagreen', markersize=4)
    # Valid colors: oranges/reds
    ax2.plot(df['epoch'], df['valid_bind_n'], 's-', label='valid_bind_n', color='tab:orange', markersize=4)
    ax2.plot(df['epoch'], df['valid_bind_cr'], 's-', label='valid_bind_cr', color='salmon', markersize=4)
    ax2.set_ylabel('Bind Loss')
    ax2.set_title('Bind and RMSD Losses vs. Epoch')

    # Right Axis (RMSD losses)
    ax2_twin = ax2.twinx()
    ax2_twin.plot(df['epoch'], df['train_rmsd'], 'd--', label='train_rmsd', color='tab:green', markersize=4)
    ax2_twin.plot(df['epoch'], df['valid_rmsd'], 'd--', label='valid_rmsd', color='olive', markersize=4)
    ax2_twin.set_ylabel('RMSD Loss')

    # Combine legends for dual axis plot
    lines, labels = ax2.get_legend_handles_labels()
    lines2, labels2 = ax2_twin.get_legend_handles_labels()
    ax2_twin.legend(lines + lines2, labels + labels2, loc='best')
    ax2.grid(True, linestyle='--', alpha=0.6)
    ax2.set_xlim(left=df['epoch'].min())

    # --- Plot 3: Top-k Accuracy ---
    ax3 = axes[2]
    ax3.plot(df['epoch'], df['valid_top1'] * 100, 'o-', label='valid_top1', color='tab:purple', markersize=4)
    ax3.plot(df['epoch'], df['valid_top5'] * 100, 's-', label='valid_top5', color='tab:pink', markersize=4)
    ax3.set_ylabel('Accuracy (%)')
    ax3.set_xlabel('Epoch')
    ax3.set_title('Top-k Accuracy vs. Epoch')
    ax3.set_ylim(0, 100)
    ax3.legend(loc='best')
    ax3.grid(True, linestyle='--', alpha=0.6)
    ax3.set_xlim(left=df['epoch'].min())

    # --- Final Touches ---
    plt.tight_layout(pad=3.0)
    
    # Save the figure
    output_filename = 'metrics_plot.png'
    plt.savefig(output_filename, dpi=300)
    plt.show()
    print(f"Plot saved to {output_filename}")

if __name__ == '__main__':
    log_file = 'train_val_log.tsv'
    try:
        metrics_df = parse_log_file(log_file)
        if not metrics_df.empty:
            plot_metrics(metrics_df)
        else:
            print(f"No data parsed from {log_file}. Please check the file format.")
    except FileNotFoundError:
        print(f"Error: The file '{log_file}' was not found.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

