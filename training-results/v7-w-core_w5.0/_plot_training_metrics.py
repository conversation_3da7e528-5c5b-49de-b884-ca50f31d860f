import re
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def parse_log_file(filepath):
    """
    Parses the custom-formatted log file into a pandas DataFrame.
    This function is adapted from the provided sample script to handle the specific log format.
    """
    data = []
    with open(filepath, 'r') as f:
        for line in f:
            # Use regex to find all key-value pairs.
            # This handles cases where keys might not have a colon, like 'train_bind_cr 3.8880'
            line = re.sub(r'(\b[\w_]+_cr)\s+([-\d.]+)', r'\1: \2', line)
            
            # Split the line into train and valid parts based on the second 'Epoch'
            parts = line.strip().split('Epoch:')[1:]
            if len(parts) < 2:
                continue
                
            train_part = "Epoch:" + parts[0]
            valid_part = "Epoch:" + parts[1]
            
            # Find all key-value pairs in each part
            train_metrics = re.findall(r'([\w_]+)[:\s]+([-\d.eE+]+)', train_part)
            valid_metrics = re.findall(r'([\w_]+)[:\s]+([-\d.eE+]+)', valid_part)

            if not train_metrics or not valid_metrics:
                continue

            # Combine and convert to a dictionary
            entry = {}
            # The epoch is the same, so we just take it from the training part
            entry['epoch'] = int(train_metrics[0][1])

            # Process metrics, skipping the epoch key
            for key, value in train_metrics[1:]:
                entry[key] = float(value)
            for key, value in valid_metrics[1:]:
                # The epoch from the validation part is redundant
                if key.lower() == 'epoch':
                    continue
                entry[key] = float(value)

            data.append(entry)
            
    return pd.DataFrame(data)

def plot_metrics(df):
    """
    Generates and saves the plots for training and validation metrics as per user specifications.
    """
    plt.style.use('seaborn-v0_8-whitegrid')
    # Create a figure with 4 subplots in 1 column, sharing the x-axis
    fig, axes = plt.subplots(4, 1, figsize=(10, 12), sharex=True)
    fig.suptitle('Training and Validation Metrics vs. Epoch', fontsize=16, y=0.95)

    # Adjust layout to minimize space between plots
    fig.subplots_adjust(hspace=0.1)

    # --- Plot 1: train_total/valid_total vs. epoch ---
    ax1 = axes[0]
    ax1.plot(df['epoch'], df['train_total'], 'o-', label='train_total', color='tab:blue', markersize=5)
    ax1.plot(df['epoch'], df['valid_total'], 's-', label='valid_total', color='lightseagreen', markersize=5)
    ax1.set_ylabel('Total Loss')
    ax1.set_title('Total Loss vs. Epoch')
    ax1.legend(loc='best')
    ax1.grid(True, which='both', linestyle='--', alpha=0.7)

    # --- Plot 2: train_bind_n/valid_bind_n/train_bind_cr/valid_bind_cr vs. epoch ---
    ax2 = axes[1]
    # Train metrics
    ax2.plot(df['epoch'], df['train_bind_n'], 'o-', label='train_bind_n', color='tab:blue', markersize=5)
    ax2.plot(df['epoch'], df['train_bind_cr'], '^-', label='train_bind_cr', color='tab:blue', markersize=5)
    # Validation metrics
    ax2.plot(df['epoch'], df['valid_bind_n'], 's-', label='valid_bind_n', color='lightseagreen', markersize=5)
    ax2.plot(df['epoch'], df['valid_bind_cr'], 'v-', label='valid_bind_cr', color='lightseagreen', markersize=5)
    ax2.set_ylabel('Bind Loss')
    ax2.set_title('Bind Loss Components vs. Epoch')
    ax2.legend(loc='best')
    ax2.grid(True, which='both', linestyle='--' , alpha=0.7)

    # --- Plot 3: train_rmsd/valid_rmsd vs. epoch ---
    ax3 = axes[2]
    ax3.plot(df['epoch'], df['train_rmsd'], 'o-', label='train_rmsd', color='tab:blue', markersize=5)
    ax3.plot(df['epoch'], df['valid_rmsd'], 's-', label='valid_rmsd', color='lightseagreen', markersize=5)
    ax3.set_ylabel('RMSD Loss')
    ax3.set_title('RMSD Loss vs. Epoch')
    ax3.legend(loc='best')
    ax3.grid(True, which='both', linestyle='--', alpha=0.7)

    # --- Plot 4: valid_top1/valid_top5 vs. epoch ---
    ax4 = axes[3]
    ax4.plot(df['epoch'], df['valid_top1'] * 100, 's-', label='valid_top1', color='lightseagreen', markersize=5)
    ax4.plot(df['epoch'], df['valid_top5'] * 100, 'v-', label='valid_top5', color='lightseagreen', markersize=5)
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Accuracy (%)')
    ax4.set_title('Top-k Validation Accuracy vs. Epoch')
    ax4.set_ylim(0, 100)
    ax4.legend(loc='best')
    ax4.grid(True, which='both', linestyle='--', alpha=0.7)

    # --- Final Touches ---
    # Remove x-tick labels for all but the last plot
    for ax in axes[:-1]:
        ax.tick_params(labelbottom=False)

    # Save the figure
    output_filename = 'training_metrics_plot.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    # plt.show() # Do not call plt.show() in a non-interactive environment
    print(f"Plot saved to {output_filename}")

if __name__ == '__main__':
    log_file = 'train_val_log.tsv'
    try:
        metrics_df = parse_log_file(log_file)
        if not metrics_df.empty:
            # Sort by epoch to ensure lines are drawn correctly
            metrics_df = metrics_df.sort_values(by='epoch').reset_index(drop=True)
            plot_metrics(metrics_df)
        else:
            print(f"No data could be parsed from '{log_file}'. Please check the file format and content.")
    except FileNotFoundError:
        print(f"Error: The file '{log_file}' was not found.")
    except KeyError as e:
        print(f"An error occurred: A required column is missing from the data: {e}")
        print("Please ensure the log file contains all necessary metrics.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")