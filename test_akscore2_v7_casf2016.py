import os, pickle
import numpy as np
import argparse

from sklearn.metrics import mean_squared_error, mean_absolute_error, auc
from torch_geometric.nn import global_mean_pool
from torch_geometric.loader import DataLoader
from torch_geometric.data import Data

import torch
import torch.nn.functional as F
from torch import nn
from torch.nn import Linear
from torch_geometric.nn import GATv2Conv
from torch_geometric.data import Dataset
from torch_geometric.data import batch as pyg_batch_func

import random
import time
import gzip

os.environ['CUDA_LAUNCH_BLOCKING'] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# DEVICE = torch.device('cpu')

BIND_AFF_MEAN = -0 # -8.7037,  -6
BIND_AFF_STD = 1  # 2.5375,    3

def one_hot_encoding(x, boundary, num_classes):

    one_hot_code = F.one_hot(torch.tensor(np.digitize(x, boundary)), num_classes=num_classes)
    return one_hot_code


            
            
                    
                    
                    

class GATv2(torch.nn.Module):
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64, dropout=0.25, cut_edge_dis=8):
        super().__init__()
        self.num_layers = num_layers
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.cut_edge_dis = cut_edge_dis

        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(dropout)

        self.protein_ligand_gnn_layers = []
        for num in range(self.num_layers):
            self.protein_ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_ligand_gnn_layers = nn.ModuleList(self.protein_ligand_gnn_layers)

        self.graph_dec_bind = nn.Sequential(nn.Linear(self.hidden_dim, self.hidden_dim),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim // 2, 1),
                                             )

        self.graph_dec_rmsd = nn.Sequential(nn.Linear(self.hidden_dim, self.hidden_dim),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim // 2, 1),
                                             )

    def forward(self, graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch):

        x, edge_index, edge_attr, batch = graph_batch.x, graph_batch.edge_index, graph_batch.edge_attr, graph_batch.batch
        protein_x, protein_edge_index, protein_edge_attr, protein_batch = protein_graph_batch.x, protein_graph_batch.edge_index, protein_graph_batch.edge_attr, protein_graph_batch.batch
        ligand_x, ligand_edge_index, ligand_edge_attr, ligand_batch = ligand_graph_batch.x, ligand_graph_batch.edge_index, ligand_graph_batch.edge_attr, ligand_graph_batch.batch
        protein_ligand_x, protein_ligand_edge_index, protein_ligand_edge_attr, protein_ligand_batch = protein_ligand_graph_batch.x, protein_ligand_graph_batch.edge_index, protein_ligand_graph_batch.edge_attr, protein_ligand_graph_batch.batch


        x = self.node_to_hidden(x)



        #### filtering edge index by distance 8A 6A 4A
        if self.cut_edge_dis == 6:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-1] != torch.Tensor([0, 0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]
        elif self.cut_edge_dis == 4:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-2] != torch.Tensor([0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]

        for layer in self.protein_ligand_gnn_layers:
            x = layer(x, edge_index, edge_attr)
            x = F.relu(x)
            x = self.dropout_layer(x)


        protein_ligand_x_dock = global_mean_pool(x, batch)  # [batch_size, hidden_channels]

        bind_logits = self.graph_dec_bind(protein_ligand_x_dock)
        rmsd_logits = self.graph_dec_rmsd(protein_ligand_x_dock)

        return bind_logits, rmsd_logits

    
    
    
    
    

class akscore2_dataset(Dataset):
    def __init__(self, pdb_id_graphs, exclude_list_path = None, train_mode = True, rmsd_cutoff=2, bind_aff_aug=[-1,1], bind_aff_rej=[-6,0]):
        super(akscore2_dataset,self).__init__()
        self.train_mode = train_mode
        self.rmsd_cutoff = rmsd_cutoff
        self.bind_aff_aug = bind_aff_aug
        self.bind_aff_rej = bind_aff_rej
        self.pdb_id_graphs = pdb_id_graphs




    def len(self):

        return len(self.pdb_id_graphs)


    def graph_modification(self, graph):
        x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()

        protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
        ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
        protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]

        protein_edge_index = edge_index[:, protein_edge_attr_idx]
        ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
        protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]

        protein_ligand_node_sep_idx = torch.min(ligand_edge_index)

        protein_x = x[:protein_ligand_node_sep_idx, :]
        ligand_x = x[protein_ligand_node_sep_idx:, :]

        protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx, :])
        ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index - torch.min(ligand_edge_index),
                            edge_attr=edge_attr[ligand_edge_attr_idx, :])

        protein_ligand_edge_attr = edge_attr[protein_ligand_edge_attr_idx, :]

        protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)

        return graph, protein_graph, ligand_graph, protein_ligand_graph
    

    def get(self, idx):
        pdb_id_graph = self.pdb_id_graphs[idx]
        
        pdb_id_graph = [pdb_id_graph]
        
        graph_list = []
        protein_graph_list=[]
        ligand_graph_list=[]
        protein_ligand_graph_list=[]

        for graph in pdb_id_graph:
            graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)

            graph_list.append(graph)
            protein_graph_list.append(protein_graph)
            ligand_graph_list.append(ligand_graph)
            protein_ligand_graph_list.append(protein_ligand_graph)

        graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
        protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
        ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
        protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)


        return graph_list_batch, protein_graph_list_batch, ligand_graph_list_batch, protein_ligand_graph_list_batch



def test_model(model, loader):
    model.eval()

    result = {
            "bind_preds": [],
            "rmsd_preds": [],
            "bind_rmsd_preds":[],
            "names": [],
        }

    with torch.no_grad():
        for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):
            print(f"{idx}/{len(loader)}")
            bind_logits, rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE), ligand_graph_batch.to(DEVICE),
                                protein_ligand_graph_batch.to(DEVICE))

            bind_rmsd_logits = bind_logits+rmsd_logits
#             result["bind_preds"].extend([x.item() for x in bind_logits])
#             result["rmsd_preds"].extend([x.item() for x in rmsd_logits])
            result["bind_rmsd_preds"].extend([x.item() for x in bind_rmsd_logits])

            from itertools import chain
            result["names"].extend(list(chain(*graph_batch.name)))


        
    return result


def save_result(result_save_dir, result, test_type="scoring"):

    result_save_dir = os.path.join(result_save_dir, test_type)
    
    if not os.path.exists(result_save_dir):
        os.makedirs(result_save_dir)

    if test_type == "scoring" or test_type == "ranking":
        result_save_path = os.path.join(result_save_dir, 'akscore2.dat')

        with open(result_save_path, 'w') as f:
            f.write("#code  score\n")
            for i in range(len(result["names"])):
                f.write(f"{result['names'][i][:4]} {result['bind_rmsd_preds'][i]:.5f}\n")

    
    if test_type == "docking":
        pdb_ids = [name[:4] for name in result['names']]
        pdb_ids_unique = list(set(pdb_ids))
        pdb_ids_unique = sorted(pdb_ids_unique)
        
        for pdb_id in pdb_ids_unique:
            indices = [i for i, x in enumerate(pdb_ids) if x == pdb_id]
            

            result_save_path = os.path.join(result_save_dir, pdb_id + '_score.dat')
            with open(result_save_path, 'w') as f:
                f.write("#code  score\n")
                for indice in indices:
                    f.write(f"{result['names'][indice]} {result['bind_rmsd_preds'][indice]:.5f}\n")            
                    
                    
                    
                    
                    

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Test Graph based Protein-Ligand Binding Affinity')
    parser.add_argument('--coreset_native_path', default=r'/Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_org2.pkl.gz', type=str, help='data path')
    
    parser.add_argument('--coreset_docking_path', default=r'/Arontier/People/junsuha/akscore_project/sim_code/coreset_datas_docking2.pkl.gz', type=str, help='data path')
    
    parser.add_argument('--result_save_dir', default=r'', type=str, help='result save directory')
    parser.add_argument('--model_path', default=r'', type=str, help='model path')
    parser.add_argument('--test_type', default=r'scoring', type=str, help='scoring, ranking, docking, screening')

    parser.add_argument('--node_dim', default=73, type=int, help="Input size of layer's node")
    parser.add_argument('--edge_dim', default=24, type=int, help='edge dimension')
    parser.add_argument('--hidden_dim', default=128, type=int, help='Hidden layer size')
    parser.add_argument('--num_layers', default=5, type=int, help='number of gnn layer')
    parser.add_argument('--dropout', default=0.25, type=float, help='dropout ratio')
    parser.add_argument('--cut_edge_dis', default=8, type=int, help='non-covalent bond edge distance')

    parser.add_argument('--batch_size', default=32, type=int, help='batch size')
    parser.add_argument('--num_workers', default=8, type=int, help="cpu worker number")


    args = parser.parse_args()
    print(args)  
    pdb_id_graphs = []
    
    if args.test_type == "scoring" or args.test_type == "docking":
        with gzip.open(args.coreset_native_path, 'rb') as f:
            coreset_native_dataset = pickle.load(f)
        pdb_id_graphs.extend(coreset_native_dataset)
         
        if args.test_type == "docking":
            with gzip.open(args.coreset_docking_path, 'rb') as f:
                coreset_docking_dataset = pickle.load(f)
            pdb_id_graphs.extend(coreset_docking_dataset)
            

    test_dataset = akscore2_dataset(pdb_id_graphs)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=args.num_workers)

    model = GATv2(args.node_dim, args.edge_dim, num_layers=args.num_layers, hidden_dim=args.hidden_dim, cut_edge_dis=args.cut_edge_dis).to(DEVICE)


    if os.path.isfile(args.model_path):
        ch = torch.load(args.model_path)
        model.load_state_dict(ch['model'])
        model.to(DEVICE)

    if args.result_save_dir == "":        
        result_save_dir = args.model_path[:-4]
        
    else:
        result_save_dir = args.result_save_dir
    
    if not os.path.exists(result_save_dir):
        os.makedirs(result_save_dir)
    
    
    result = test_model(model, test_loader)
    
    save_result(result_save_dir, result, test_type=args.test_type)







