import rdkit.Chem as Chem


pdb = './junsu-data-sample/redocking_general_set/1e66/1e66_protein.pdb'

mol1 = Chem.MolFromPDBFile(
            pdb, removeHs=False, proximityBonding=True, sanitize=True
        )

# I want to write mol1 as a new PDB file named '1e66_new.pdb'
# Write mol1 to a new PDB file
if mol1 is not None:
    writer = Chem.PDBWriter('1e66_new.pdb')
    writer.write(mol1)
    writer.close()
else:
    print("Failed to create molecule from PDB file")
 
