import os, pickle
import numpy as np
import argparse
from sklearn.metrics import roc_auc_score

from sklearn.metrics import mean_squared_error, mean_absolute_error, auc
from torch_geometric.nn import global_mean_pool
from torch_geometric.loader import DataLoader
from torch_geometric.data import Data

from itertools import chain
import torch
import torch.nn.functional as F
from torch import nn
from torch.nn import Linear
from torch_geometric.nn import GATv2Conv
from torch_geometric.data import Dataset
from torch_geometric.data import batch as pyg_batch_func

import random
import time
import gzip

os.environ['CUDA_LAUNCH_BLOCKING'] = "1"
#os.environ["CUDA_VISIBLE_DEVICES"] = "0"

DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# DEVICE = torch.device('cpu')

BIND_AFF_MEAN = -0 # -8.7037,  -6
BIND_AFF_STD = 1  # 2.5375,    3

print(f"BIND_AFF_MEAN: {BIND_AFF_MEAN}, BIND_AFF_STD: {BIND_AFF_STD}")

def one_hot_encoding(x, boundary, num_classes):
    """
    将连续值转换为独热编码。

    Args:
        x (float): 输入的连续值。
        boundary (list or np.array): 用于划分区间的边界值。
        num_classes (int): 独热编码的类别数。

    Returns:
        torch.Tensor: 转换后的独热编码张量。
    """

    one_hot_code = F.one_hot(torch.tensor(np.digitize(x, boundary)), num_classes=num_classes)
    return one_hot_code

class GATv2(torch.nn.Module):
    """
    基于图注意力网络v2 (GATv2) 的模型，用于预测蛋白质-配体相互作用。
    该模型分别处理蛋白质和配体的图，然后将它们的全局特征拼接起来进行最终预测。
    """
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64, dropout=0.1):
        """
        初始化GATv2模型。

        Args:
            node_dim (int): 节点特征的维度。
            edge_dim (int): 边特征的维度。
            num_layers (int, optional): GNN层的数量。默认为 5。
            hidden_dim (int, optional): 隐藏层的维度。默认为 64。
            dropout (float, optional): Dropout的比率。默认为 0.1。
        """
        super().__init__()
        self.num_layers = num_layers
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(dropout)


        self.protein_gnn_layers = []
        for num in range(self.num_layers): # GNN layers for protein 
            self.protein_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_gnn_layers = nn.ModuleList(self.protein_gnn_layers)

        self.ligand_gnn_layers = []
        for num in range(self.num_layers//2+1): # GNN layers for ligand
            self.ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.ligand_gnn_layers = nn.ModuleList(self.ligand_gnn_layers)


        self.graph_dec_rmsd = nn.Sequential(nn.Linear(self.hidden_dim*2, self.hidden_dim*2),
                                       nn.ReLU(),
                                       nn.Dropout(dropout),
                                       nn.Linear(self.hidden_dim*2, self.hidden_dim),
                                       nn.ReLU(),
                                       nn.Dropout(dropout),
                                       nn.Linear(self.hidden_dim, 1),)


    def forward(self, graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch):
        """
        定义模型的前向传播过程。

        Args:
            graph_batch (torch_geometric.data.Batch): 完整的蛋白质-配体复合物图批次。
            protein_graph_batch (torch_geometric.data.Batch): 仅包含蛋白质的图批次。
            ligand_graph_batch (torch_geometric.data.Batch): 仅包含配体的图批次。
            protein_ligand_graph_batch (torch_geometric.data.Batch): 包含蛋白质-配体相互作用边的图批次。

        Returns:
            torch.Tensor: RMSD预测的 logits 值。
        """

        x, edge_index, edge_attr, batch = graph_batch.x, graph_batch.edge_index, graph_batch.edge_attr, graph_batch.batch
        protein_x, protein_edge_index, protein_edge_attr, protein_batch = protein_graph_batch.x, protein_graph_batch.edge_index, protein_graph_batch.edge_attr, protein_graph_batch.batch
        ligand_x, ligand_edge_index, ligand_edge_attr, ligand_batch = ligand_graph_batch.x, ligand_graph_batch.edge_index, ligand_graph_batch.edge_attr, ligand_graph_batch.batch
        protein_ligand_x, protein_ligand_edge_index, protein_ligand_edge_attr, protein_ligand_batch = protein_ligand_graph_batch.x, protein_ligand_graph_batch.edge_index, protein_ligand_graph_batch.edge_attr, protein_ligand_graph_batch.batch

        protein_x = self.node_to_hidden(protein_x)
        ligand_x = self.node_to_hidden(ligand_x)

        for layer in self.protein_gnn_layers:
            protein_x = layer(protein_x, protein_edge_index, protein_edge_attr)
            protein_x = F.relu(protein_x)
            protein_x = self.dropout_layer(protein_x)

        for layer in self.ligand_gnn_layers:
            ligand_x = layer(ligand_x, ligand_edge_index, ligand_edge_attr)
            ligand_x = F.relu(ligand_x)
            ligand_x = self.dropout_layer(ligand_x)


        protein_x_feat = global_mean_pool(protein_x, protein_batch)  # [batch_size, hidden_channels]
        ligand_x_feat = global_mean_pool(ligand_x, ligand_batch)  # [batch_size, hidden_channels]

        protein_ligand_x_cat = torch.concat((protein_x_feat, ligand_x_feat), dim=-1)

        rmsd_logits = self.graph_dec_rmsd(protein_ligand_x_cat)

        return rmsd_logits


    

class akscore2_dataset(Dataset):
    """
    用于加载Akscore2模型所需数据的自定义数据集类。
    它负责读取PDB ID列表，加载对应的图数据文件，并进行预处理。
    """
    def __init__(self, data_dir, pdb_id_list_path, exclude_list_path=None, train_mode=True, rmsd_cutoff=2,
                 bind_aff_aug=[-1, 1], bind_aff_rej=[-6, 0]):
        """
        初始化数据集。

        Args:
            data_dir (str): 数据所在的根目录。
            pdb_id_list_path (str): 包含PDB ID列表的文件路径。
            exclude_list_path (str, optional): 需要排除的PDB ID列表文件路径。默认为 None。
            train_mode (bool, optional): 是否为训练模式。True表示训练模式，False表示验证/测试模式。默认为 True。
            rmsd_cutoff (float, optional): 用于将RMSD转换为二分类标签的阈值。默认为 2。
            bind_aff_aug (list, optional): 结合亲和力数据增强的范围。默认为 [-1, 1]。
            bind_aff_rej (list, optional): 结合亲和力拒绝的范围。默认为 [-6, 0]。
        """
        super(akscore2_dataset, self).__init__()
        self.train_mode = train_mode
        self.data_dir = data_dir
        self.rmsd_cutoff = rmsd_cutoff
        self.bind_aff_aug = bind_aff_aug
        self.bind_aff_rej = bind_aff_rej

        self.native_paths = []
        self.decoy_dock_paths = []
        self.decoy_cross_paths = []
        self.decoy_random_paths = []
        self.train_paths = []
        self.valid_paths = []


        with open(pdb_id_list_path) as f:
            self.pdb_id_list = f.readlines()

        self.pdb_id_list = [line.strip('\n') for line in self.pdb_id_list]
        if exclude_list_path:
            with open(exclude_list_path) as f:
                exclude_pdb_id_list = f.readlines()
            exclude_pdb_id_list = [line.strip('\n') for line in exclude_pdb_id_list]
            self.pdb_id_list = list(set(self.pdb_id_list) - set(exclude_pdb_id_list))

        for pdb_id in self.pdb_id_list:
            pdb_id_dir = os.path.join(self.data_dir, pdb_id)
            pdb_id_filenames = [f for f in os.listdir(pdb_id_dir) if f.endswith('.pkl')]
            valid_dump_list = []
            for pdb_id_filename in pdb_id_filenames:
                pdb_id_file_path = os.path.join(pdb_id_dir, pdb_id_filename)
                name_splited = pdb_id_filename.split('_')
                if name_splited[-2] == '0':
                    self.native_paths.append(pdb_id_file_path)
                    valid_dump_list.append(pdb_id_file_path)
                elif name_splited[-2] == '1':
                    self.decoy_dock_paths.append(pdb_id_file_path)
                    ######## 
#                     valid_dump_list.append(pdb_id_file_path)

                elif name_splited[-2] == '2':
                    self.decoy_cross_paths.append(pdb_id_file_path)
                    valid_dump_list.append(pdb_id_file_path)
                elif name_splited[-2] == '3':
                    self.decoy_random_paths.append(pdb_id_file_path)
                    valid_dump_list.append(pdb_id_file_path)

            self.valid_paths.append(valid_dump_list)
        
        if self.train_mode:
            self.make_dataset()


    def make_dataset(self):
        """
        构建训练数据集。
        通过对不同类型的干扰项（decoys）进行采样，来创建一个均衡的训练集。
        这里将天然构象、交叉构象和随机构象按一定比例混合。
        """
        ##### data ratio 122 144

        len_natives = len(self.native_paths)
        self.train_paths = []
        self.train_paths.extend(self.native_paths)
#         self.train_paths.extend(random.sample(self.decoy_dock_paths, len_natives))
        self.train_paths.extend(random.sample(self.decoy_cross_paths, len_natives*2))
        self.train_paths.extend(random.sample(self.decoy_random_paths, len_natives*2))
        random.shuffle(self.train_paths)


    def len(self):
        """
        返回数据集的大小。
        在训练模式下，返回训练样本的数量；否则，返回验证/测试PDB ID的数量。
        """
        #         return 30

        if self.train_mode:
            len_dataset = len(self.train_paths)
        else:
            len_dataset = len(self.valid_paths)

        return len_dataset

    

    def graph_rmsd_bindaff_aug(self, graph):
        """
        对图数据进行增强，主要是处理RMSD标签。
        将RMSD值根据cutoff转换为二分类标签（0或1）。
        - RMSD > cutoff -> 标签为 0 (差构象)
        - RMSD <= cutoff -> 标签为 1 (好构象)

        Args:
            graph (torch_geometric.data.Data): 输入的图数据。

        Returns:
            torch_geometric.data.Data: 处理了RMSD标签后的图数据。
        """
        if graph.y > self.rmsd_cutoff:
            graph.y = torch.zeros_like(graph.y)

        else:
            graph.y = torch.ones_like(graph.y)
            
        graph.y = graph.y.float()
        
        return graph



    def graph_modification(self, graph):
        """
        修改和拆分输入的复合物图。
        该函数执行以下操作：
        1. 从节点和边特征中移除对接程序产生的特定特征。
        2. 根据边的类型（蛋白内部、配体内部、蛋白-配体之间），将复合物图拆分为三个独立的图：
           - protein_graph: 只包含蛋白质原子和内部连接。
           - ligand_graph: 只包含配体原子和内部连接。
           - protein_ligand_graph: 包含所有原子，但只包含蛋白质和配体之间的相互作用边。

        Args:
            graph (torch_geometric.data.Data): 原始的蛋白质-配体复合物图。

        Returns:
            tuple: 包含四个图对象的元组 (原始图, 蛋白质图, 配体图, 蛋白质-配体相互作用图)。
        """
        x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()
        

        protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
        ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
        protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]
        

        # 这个地方很有问题... ? 好像也没错...
        #### remove docking feature for platform app 
        x = torch.concat((x[:,:-5], x[:,-4:]),axis=1) # 删去了一个: 73-1=72
        edge_attr = edge_attr[:,3:-9]  # 删去了前3个(类型)和后9个特征(protein-ligand interaction), 24-12=12 
        
        
        protein_edge_index = edge_index[:, protein_edge_attr_idx]
        ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
        protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]

        protein_ligand_node_sep_idx = torch.min(ligand_edge_index)

        protein_x = x[:protein_ligand_node_sep_idx, :]
        ligand_x = x[protein_ligand_node_sep_idx:, :]
        
        
        
        
        
        protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx,:])
        ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index-torch.min(ligand_edge_index), edge_attr=edge_attr[ligand_edge_attr_idx,:])

        protein_ligand_edge_attr = edge_attr[protein_ligand_edge_attr_idx,:]
        

        protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)

        return graph, protein_graph, ligand_graph, protein_ligand_graph



    def get(self, idx):
        """
        获取数据集中指定索引的样本。

        在训练模式下，它加载单个图数据。
        在验证/测试模式下，它加载与一个PDB ID相关的所有图数据（天然构象和各种干扰项）。

        Args:
            idx (int): 样本的索引。

        Returns:
            tuple: 包含四个批处理后的图对象的元组。
                   (批处理后的复合物图, 批处理后的蛋白质图, 批处理后的配体图, 批处理后的蛋白质-配体图)
        """
        pdb_id_graphs = []
        if self.train_mode:
            graph_path = self.train_paths[idx]
            with open(graph_path, 'rb') as f:
                pdb_id_graph = pickle.load(f)
            pdb_id_graphs.append(pdb_id_graph)
        else:
            graph_paths = self.valid_paths[idx]
            for graph_path in graph_paths:
                with open(graph_path, 'rb') as f:
                    pdb_id_graph = pickle.load(f)
                pdb_id_graphs.append(pdb_id_graph)

        graph_list = []
        protein_graph_list = []
        ligand_graph_list = []
        protein_ligand_graph_list = []
        for graph in pdb_id_graphs:
            graph = self.graph_rmsd_bindaff_aug(graph)
            graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)

            graph_list.append(graph)
            protein_graph_list.append(protein_graph)
            ligand_graph_list.append(ligand_graph)
            protein_ligand_graph_list.append(protein_ligand_graph)
            
            

        graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
        protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
        ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
        protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)


        return graph_list_batch, protein_graph_list_batch, ligand_graph_list_batch, protein_ligand_graph_list_batch

    
    
    
    
    
    


def valid_model_epoch(model, loader, criterion):
    """
    在一个epoch中对模型进行验证。

    Args:
        model (torch.nn.Module): 待验证的模型。
        loader (torch_geometric.loader.DataLoader): 验证数据加载器。
        criterion (dict): 包含损失函数的字典。

    Returns:
        dict: 包含该epoch验证结果的字典，如损失、AUC、Top-N成功率等。
    """
    model.eval()
    epoch_loss = {
        "total": 0.0,
        "bind_aff": 0.0,
        "rmsd": 0.0,
    }
    
    bind_preds = []
    bind_gts = []
    rmsd_preds = []
    rmsd_gts = []
    
    names = []
    success_count= {
        "top1": 0.0,
        "top5": 0.0,
    }
    target_count = 0
    with torch.no_grad():
        for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):
            
            rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE), ligand_graph_batch.to(DEVICE), protein_ligand_graph_batch.to(DEVICE))

            
            loss_rmsd = criterion['bce'](rmsd_logits, graph_batch.y)
            
            epoch_loss["rmsd"] = epoch_loss["rmsd"] + loss_rmsd.item()

            print(f"Validation: {idx}/{len(loader)}, RMSD Loss: {loss_rmsd:.4f}")

            rmsd_logits_sigmoid = torch.sigmoid(rmsd_logits)

            rmsd_preds.extend([x.item() for x in rmsd_logits_sigmoid])
            rmsd_gts.extend([x.item() for x in graph_batch.y])
            
            names.extend(graph_batch.name)


            native_index = [i_n for i_n, name in enumerate(graph_batch.name[0]) if '_0_0' in name][0]
            #### success rate in top n
            if len(rmsd_logits_sigmoid) > 50:
                top_list = torch.topk(rmsd_logits_sigmoid, 5, dim=0, largest=True).indices.squeeze().tolist()
                target_count = target_count + 1
                if native_index in top_list[:5]:
                    success_count["top5"] += 1
                if top_list[0] == native_index:
                    success_count["top1"] += 1
                
 
    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key] / len(loader)
    
    
    
    epoch_loss["auc"] = roc_auc_score(rmsd_gts, rmsd_preds)
    epoch_loss["suc_top1"] = success_count["top1"] / target_count
    epoch_loss["suc_top5"] = success_count["top5"] / target_count
    
    print(f'auc {epoch_loss["auc"]}')
    print(f'top1 {success_count["top1"] / target_count}')
    print(f'top5 {success_count["top5"] / target_count}')

    
#     ########################################################################
#     from itertools import chain
#     names = list(chain(*names))
#     print(names)

#     with open("train_coreset_rmsd.dat", 'w') as f:
#         f.write("#code  \t rmsd_gt \t rmsd_pred\n")
#         for i in range(len(names)):
#             f.write(f"{names[i]} \t {rmsd_gts[i]:.3f} \t {rmsd_preds[i]:.3f}\n")
    

#     exit(0)
    
    return epoch_loss


def train_model_epoch(model, loader, criterion, optimizer):
    """
    在一个epoch中对模型进行训练。

    Args:
        model (torch.nn.Module): 待训练的模型。
        loader (torch_geometric.loader.DataLoader): 训练数据加载器。
        criterion (dict): 包含损失函数的字典。
        optimizer (torch.optim.Optimizer): 优化器。

    Returns:
        tuple: 包含训练后的模型和该epoch训练结果字典的元组。
    """
    model.train()
    epoch_loss = {
        "total": 0.0,
        "bind_aff": 0.0,
        "rmsd": 0.0,
    }
    
    rmsd_preds = []
    rmsd_gts = []
    
    for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):

        rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE), ligand_graph_batch.to(DEVICE), protein_ligand_graph_batch.to(DEVICE))

        loss_rmsd = criterion['bce'](rmsd_logits, graph_batch.y)
        

        optimizer.zero_grad()
        loss_rmsd.backward()
#         nn.utils.clip_grad_value_(model.parameters(), clip_value=1.0)
#         nn.utils.clip_grad_norm_(model.parameters(), max_norm=2.0, norm_type=2)
        optimizer.step()

        epoch_loss["rmsd"] = epoch_loss["rmsd"] + loss_rmsd.item()

        print(f"Train: {idx}/{len(loader)}, RMSD Loss: {loss_rmsd:.4f}")
        
        
        rmsd_logits_sigmoid = torch.sigmoid(rmsd_logits)
        rmsd_preds.extend([x.item() for x in rmsd_logits_sigmoid])
        rmsd_gts.extend([x.item() for x in graph_batch.y])
        

        
        
#         if idx % 10 == 0:
#             print(batch.name)
#             print(bind_aff_logits)
#             print(batch.bind)
#             print(torch.sigmoid(rmsd_logits))
#             print(batch.y)
        
        
        
    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key]/len(loader)
    

    epoch_loss["auc"] = roc_auc_score(rmsd_gts, rmsd_preds)

    print(epoch_loss["auc"])
    
    return model, epoch_loss





if __name__ == '__main__':
    # --- 1. 参数解析 ---
    # 定义和解析命令行参数，用于配置训练过程。
    parser = argparse.ArgumentParser(description='Graph based Protein-Ligand Binding Affinity Regression')
    parser.add_argument('--data_dir', default=r'', type=str, help='data directory')
    parser.add_argument('--train_list_path', default=r'', type=str, help='train list path')
    parser.add_argument('--valid_list_path', default=r'', type=str, help='validation list path')
    parser.add_argument('--test_list_path', default=r'', type=str, help='test list path')

    parser.add_argument('--exclude_list_path', default=r'', type=str, help='exclude list path')

    parser.add_argument('--model_save_dir', default=r'', type=str, help='model save directory')
    parser.add_argument('--resume_model_path', default=r'', type=str, help='resume model path')
    parser.add_argument('--num_workers', default=8, type=int, help="cpu worker number")
    parser.add_argument('--batch_size', default=32, type=int, help='batch size')

    parser.add_argument('--lr', default=0.001, type=float, help='Learnig Rate') # 0.0001 
    parser.add_argument('--node_dim', default=72, type=int, help="Input size of layer's node")
    parser.add_argument('--edge_dim', default=12, type=int, help='edge dimension')
    parser.add_argument('--hidden_dim', default=256, type=int, help='Hidden layer size')
    parser.add_argument('--num_layers', default=5, type=int, help='number of gnn layer')
    parser.add_argument('--dropout', default=0.25, type=float, help='dropout ratio') # 0.1
    parser.add_argument('--max_epoch', default=2000, type=int, help='maximum number of epochs') 

    parser.add_argument('--rmsd_cutoff', default=2, type=int, help='RMSD threshold for binary classification')
    parser.add_argument('--bind_aff_aug_upper', default=1, type=int, help='upper bound for binding affinity augmentation')
    parser.add_argument('--bind_aff_aug_lower', default=-1, type=int, help='lower bound for binding affinity augmentation')
    parser.add_argument('--bind_aff_rej_upper', default=0, type=int, help='upper bound for binding affinity rejection')
    parser.add_argument('--bind_aff_rej_lower', default=-5, type=int, help='lower bound for binding affinity rejection')


    args = parser.parse_args()
    print(args)
    model_save_dir = os.path.join(args.model_save_dir, f"lr{args.lr}_bs{args.batch_size}_nd{args.node_dim}_ed{args.edge_dim}_hd{args.hidden_dim}_nl{args.num_layers}_do{args.dropout}")

        
    # --- 2. 数据集和数据加载器设置 ---
    # 根据解析的参数创建训练和验证数据集实例。
    train_dataset = akscore2_dataset(args.data_dir, args.train_list_path, exclude_list_path=args.exclude_list_path, train_mode=True,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])
    valid_dataset = akscore2_dataset(args.data_dir, args.valid_list_path, exclude_list_path=args.exclude_list_path, train_mode=False,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])
    
#     test_dataset = akscore2_dataset(args.data_dir, args.test_list_path, exclude_list_path=args.exclude_list_path, train_mode=False,
#                                      rmsd_cutoff=args.rmsd_cutoff,
#                                      bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
#                                      bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])
    
    
    
    
    # 创建数据加载器，用于批量加载数据。
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers)
    val_loader = DataLoader(valid_dataset, batch_size=1, shuffle=False, num_workers=args.num_workers)
#     test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=args.num_workers)
    

#     print(f"train len: {len(train_loader)}, val len: {len(val_loader)}, test len: {len(test_loader)},")
    print(f"train len: {len(train_loader)}, val len: {len(val_loader)},")

    
    loader = {
        'train_loader': train_loader,
        'val_loader': val_loader,
#         'test_loader': test_loader,

    }

    # --- 3. 模型、损失函数和优化器初始化 ---
    # 初始化GATv2模型，并将其移动到指定的设备（GPU或CPU）。
    model = GATv2(args.node_dim, args.edge_dim, num_layers=args.num_layers, hidden_dim=args.hidden_dim, dropout=args.dropout).to(DEVICE)
    
    pos_weight = torch.ones([1])*4

    # 定义损失函数。这里使用了均方误差（MSE）和带logits的二元交叉熵（BCEWithLogitsLoss）。
    criterion = {
        'mse': torch.nn.MSELoss(),
#         'bce': torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight.to(DEVICE)),
        'bce': torch.nn.BCEWithLogitsLoss(),

        }
    
#     optimizer = torch.optim.RAdam(model.parameters(), lr=args.lr)
    # 定义优化器，这里使用Adam优化器。
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)

    ### 如果提供了模型路径，则加载预训练模型以恢复训练。
    resume_epoch=1
    if os.path.isfile(args.resume_model_path):
        ch = torch.load(args.resume_model_path)
        model.load_state_dict(ch['model'])
        model.to(DEVICE)
        resume_epoch = ch['epoch']+1
        optimizer.load_state_dict(ch['optimizer'])
#         model_save_dir = model_save_dir+f"_resume_lr{args.lr}"
        for g in optimizer.param_groups:
            g['lr'] = args.lr
        print(f"model loaded: {args.resume_model_path}")


    if not os.path.exists(model_save_dir):
        os.makedirs(model_save_dir)

    fconv = open(os.path.join(model_save_dir, 'train_val_log.csv'), 'a')
    fconv.write('epoch, train_rmsd_loss, train_auc, valid_rmsd_loss, valid_auc, valid_suc_top1, valid_suc_top5,\n')
    fconv.close()

    # --- 4. 训练主循环 ---
    # 循环指定的epoch次数，执行模型训练和验证。
    for epoch in range(resume_epoch, args.max_epoch):
        print("=" * 120)
        print(f"Epoch {epoch} Start:")
        
        # 每个epoch开始时，重新构建训练数据集以增加随机性。
        train_dataset.make_dataset()
        # 训练一个epoch
        model, avg_epoch_train_loss = train_model_epoch(model, loader['train_loader'], criterion, optimizer)
        time.sleep(2)


        print("\n\n")
        print(f'Epoch Train: {epoch}, '
              f'RMSD Loss: {avg_epoch_train_loss["rmsd"]:.4f}, AUC: {avg_epoch_train_loss["auc"]:.4f}')



        # 每10个epoch，在验证集上评估模型性能。
        if epoch % 10 == 0:
            
            # 验证一个epoch
            avg_epoch_val_loss = valid_model_epoch(model, loader['val_loader'], criterion)
            time.sleep(2)

#             avg_epoch_test_loss = valid_model_epoch(model, loader['test_loader'], criterion)
#             time.sleep(2)
            
            print(f'Epoch Valid: {epoch}, RMSD Loss: {avg_epoch_val_loss["rmsd"]:.4f}, AUC: {avg_epoch_val_loss["auc"]:.4f}, SUC Top1:  {avg_epoch_val_loss["suc_top1"]:.4f}, SUC Top5:  {avg_epoch_val_loss["suc_top5"]:.4f}')
#             print(f'Epoch Test: {epoch}, RMSD Loss: {avg_epoch_test_loss["rmsd"]:.4f}, AUC: {avg_epoch_test_loss["auc"]:.4f}, SUC Top1:  {avg_epoch_test_loss["suc_top1"]:.4f}, SUC Top5:  {avg_epoch_test_loss["suc_top5"]:.4f}')

            
            # --- 5. 模型保存与日志记录 ---
            # 将模型状态、优化器状态和当前epoch保存为检查点文件。
            obj = {
                'epoch': epoch,
                'model': model.state_dict(),
                'optimizer': optimizer.state_dict(),
            }
            checkpoint_filename = f"checkpoint_{epoch}_tl{avg_epoch_train_loss['rmsd']:.3f}_tc{avg_epoch_train_loss['auc']:.3f}_vl{avg_epoch_val_loss['rmsd']:.3f}_vc{avg_epoch_val_loss['auc']:.3f}.pth"
            torch.save(obj, os.path.join(model_save_dir, checkpoint_filename))

            time.sleep(5)

            fconv = open(os.path.join(model_save_dir, 'train_val_log.csv'), 'a')
            fconv.write('{:.4f}, {:.4f}, {:.4f}, {:.4f}, {:.4f}, {:.4f}, {:.4f},\n'.format(epoch, avg_epoch_train_loss["rmsd"], avg_epoch_train_loss["auc"], avg_epoch_val_loss["rmsd"], avg_epoch_val_loss["auc"], avg_epoch_val_loss["suc_top1"], avg_epoch_val_loss["suc_top5"],))
            fconv.close()






