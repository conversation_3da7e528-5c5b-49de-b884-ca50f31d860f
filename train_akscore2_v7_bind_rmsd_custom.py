import os, pickle
import numpy as np
import argparse
from sklearn.metrics import mean_squared_error, mean_absolute_error, auc
from torch_geometric.nn import global_mean_pool
from torch_geometric.loader import DataLoader
from torch_geometric.data import Data
from sklearn.metrics import roc_auc_score
import torch
import torch.nn.functional as F
from torch import nn
from torch.nn import Linear
from torch_geometric.nn import GATv2Conv
from torch_geometric.data import Dataset
from torch_geometric.data import batch as pyg_batch_func
import random
import time
import gzip
from itertools import chain
from tqdm import tqdm


DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# DEVICE = torch.device('cpu')

class GATv2(torch.nn.Module):
    """
    GATv2 模型定义。
    这是一个基于图注意力网络v2 (GATv2) 的模型，用于同时预测蛋白质-配体复合物的结合亲和力(binding affinity)和RMSD。
    该模型直接在整个复合物图上进行操作，学习原子间的相互作用。
    """
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64, dropout=0.25, cut_edge_dis=8):
        """
        初始化GATv2模型。

        参数:
            node_dim (int): 输入图中节点(原子)特征的维度。
            edge_dim (int): 输入图中边(化学键或相互作用)特征的维度。
            num_layers (int): GNN层的数量。
            hidden_dim (int): GNN层中隐藏特征的维度。
            dropout (float): Dropout的比率，用于正则化。
            cut_edge_dis (int): 边距离截断值。用于在GNN计算中筛选边，只考虑特定距离阈值内的相互作用。
                                例如 8, 6, 4 (Ångström)。
        """
        super().__init__()
        self.num_layers = num_layers
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.cut_edge_dis = cut_edge_dis

        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(dropout)

        self.protein_ligand_gnn_layers = []
        for num in range(self.num_layers):
            self.protein_ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_ligand_gnn_layers = nn.ModuleList(self.protein_ligand_gnn_layers)

        self.graph_dec_bind = nn.Sequential(nn.Linear(self.hidden_dim, self.hidden_dim),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim // 2, 1),
                                             )

        self.graph_dec_rmsd = nn.Sequential(nn.Linear(self.hidden_dim, self.hidden_dim),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                                             nn.ReLU(),
                                             nn.Dropout(dropout),
                                             nn.Linear(self.hidden_dim // 2, 1),
                                             )

    def forward(self, graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch):
        """
        定义模型的前向传播过程。

        参数:
            graph_batch (torch_geometric.data.Batch): 包含整个复合物图的批次数据。
            protein_graph_batch (torch_geometric.data.Batch): 仅包含蛋白质图的批次数据 (当前版本未使用)。
            ligand_graph_batch (torch_geometric.data.Batch): 仅包含配体图的批次数据 (当前版本未使用)。
            protein_ligand_graph_batch (torch_geometric.data.Batch): 包含蛋白质-配体相互作用边的图的批次数据 (当前版本未使用)。

        返回:
            tuple[torch.Tensor, torch.Tensor]:
                - bind_logits (torch.Tensor): 预测的结合亲和力值。
                - rmsd_logits (torch.Tensor): 预测的RMSD值。
        """

        x, edge_index, edge_attr, batch = graph_batch.x, graph_batch.edge_index, graph_batch.edge_attr, graph_batch.batch
        protein_x, protein_edge_index, protein_edge_attr, protein_batch = protein_graph_batch.x, protein_graph_batch.edge_index, protein_graph_batch.edge_attr, protein_graph_batch.batch
        ligand_x, ligand_edge_index, ligand_edge_attr, ligand_batch = ligand_graph_batch.x, ligand_graph_batch.edge_index, ligand_graph_batch.edge_attr, ligand_graph_batch.batch
        protein_ligand_x, protein_ligand_edge_index, protein_ligand_edge_attr, protein_ligand_batch = protein_ligand_graph_batch.x, protein_ligand_graph_batch.edge_index, protein_ligand_graph_batch.edge_attr, protein_ligand_graph_batch.batch


        x = self.node_to_hidden(x)



        #### filtering edge index by distance 8A 6A 4A
        if self.cut_edge_dis == 6:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-1] != torch.Tensor([0, 0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]
        elif self.cut_edge_dis == 4:
            edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-2] != torch.Tensor([0, 0]).to(DEVICE)).any(dim=1))[0]
            edge_index = edge_index[:, edge_attr_idx_filtered]
            edge_attr = edge_attr[edge_attr_idx_filtered, :]

        for layer in self.protein_ligand_gnn_layers:
            x = layer(x, edge_index, edge_attr)
            x = F.relu(x)
            x = self.dropout_layer(x)


        protein_ligand_x_dock = global_mean_pool(x, batch)  # [batch_size, hidden_channels]

        bind_logits = self.graph_dec_bind(protein_ligand_x_dock)
        rmsd_logits = self.graph_dec_rmsd(protein_ligand_x_dock)

        return bind_logits, rmsd_logits


class akscore2_dataset(Dataset):
    """
    自定义数据集类，用于加载和预处理Akscore2所需的数据。
    该类负责从文件中读取蛋白质-配体图数据，并根据训练或验证模式进行不同的采样和数据增强。
    """
    def __init__(self, data_dir, pdb_id_list_path, exclude_list_path=None, train_mode=True,
                 rmsd_cutoff=2, bind_aff_aug=[-1, 1], bind_aff_rej=[-6, 0]):
        """
        初始化数据集。

        参数:
            data_dir (str): 存放图数据文件的根目录。
            pdb_id_list_path (str): 包含PDB ID列表的文件路径，用于确定要加载的数据。
            exclude_list_path (str, optional): 包含需要排除的PDB ID列表的文件路径。默认为None。
            train_mode (bool): 是否为训练模式。如果是，则会启用数据增强和复杂的负采样策略。默认为True。
            rmsd_cutoff (int): RMSD阈值，用于分类任务 (在此v7脚本中未使用)。
            bind_aff_aug (list): 结合亲和力数据增强的范围 [min, max]。
            bind_aff_rej (list): 结合亲和力拒绝区域的范围，用于损失计算 (在此v7脚本中未使用)。
        """
        super(akscore2_dataset, self).__init__()
        self.train_mode = train_mode
        self.data_dir = data_dir

        self.rmsd_cutoff = rmsd_cutoff
        self.bind_aff_aug = bind_aff_aug
        self.bind_aff_rej = bind_aff_rej

        self.path_dict_list = []

        with open(pdb_id_list_path) as f:
            self.pdb_id_list = f.readlines()

        self.pdb_id_list = [line.strip('\n') for line in self.pdb_id_list]
        if exclude_list_path:
            with open(exclude_list_path) as f:
                exclude_pdb_id_list = f.readlines()
            exclude_pdb_id_list = [line.strip('\n') for line in exclude_pdb_id_list]
            self.pdb_id_list = list(set(self.pdb_id_list) - set(exclude_pdb_id_list))

        for pdb_id in tqdm(self.pdb_id_list, desc=f'{"TRAIN" if self.train_mode else "VALID"} SET INDEXING'):
            pdb_id_dir = os.path.join(self.data_dir, pdb_id)
            pdb_id_filenames = [f for f in os.listdir(pdb_id_dir) if f.endswith('.pkl')]
            dump_paths = {
                "native": [],
                "dock": [],
                "cross": [],
                "random": [],
            }
            for pdb_id_filename in pdb_id_filenames:
                pdb_id_file_path = os.path.join(pdb_id_dir, pdb_id_filename)
                name_splited = pdb_id_filename.split('_')

                if name_splited[-2] == '0':
                    dump_paths["native"].append(pdb_id_file_path)
                elif name_splited[-2] == '1':
                    dump_paths["dock"].append(pdb_id_file_path)
                elif name_splited[-2] == '2':
                    dump_paths["cross"].append(pdb_id_file_path)
                elif name_splited[-2] == '3':
                    dump_paths["random"].append(pdb_id_file_path)

            self.path_dict_list.append(dump_paths)


        self.len_dataset = len(self.path_dict_list)

    def len(self):
        """返回数据集中PDB ID的数量。"""
        #         return 30
        return self.len_dataset



    def graph_rmsd_bindaff_aug(self, graph):
        """
        对单个图数据进行数据增强和格式化。
        在训练模式下，对结合亲和力标签(graph.bind)应用一个均匀分布的随机扰动。
        同时，将结合亲和力和RMSD标签转换为指定格式的torch张量。

        参数:
            graph (torch_geometric.data.Data): 单个图数据对象。

        返回:
            torch_geometric.data.Data: 处理后的图数据对象。
        """
        if self.train_mode:
            graph.bind = graph.bind + np.random.uniform(self.bind_aff_aug[0], self.bind_aff_aug[1])


        graph.bind = torch.as_tensor(graph.bind, dtype=torch.float32).unsqueeze(-1).unsqueeze(-1)

        graph.y = graph.y.float()

        return graph

    def graph_modification(self, graph):
        """
        将一个完整的蛋白质-配体复合物图分解为多个子图。
        尽管在此v7脚本的`forward`函数中这些子图未被直接使用，
        但此函数提供了将复合物图分解为纯蛋白质图、纯配体图和仅包含相互作用边的图的能力。

        参数:
            graph (torch_geometric.data.Data): 原始的蛋白质-配体复合物图。

        返回:
            tuple: 包含四个图对象的元组:
                - graph (Data): 原始图。
                - protein_graph (Data): 仅包含蛋白质原子和内部边的图。
                - ligand_graph (Data): 仅包含配体原子和内部边的图。
                - protein_ligand_graph (Data): 包含所有原子但仅含蛋白质-配体相互作用边的图。
        """
        x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()

        protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
        ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
        protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]

        protein_edge_index = edge_index[:, protein_edge_attr_idx]
        ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
        protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]

        protein_ligand_node_sep_idx = torch.min(ligand_edge_index)

        protein_x = x[:protein_ligand_node_sep_idx, :]
        ligand_x = x[protein_ligand_node_sep_idx:, :]

        protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx, :])
        ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index - torch.min(ligand_edge_index),
                            edge_attr=edge_attr[ligand_edge_attr_idx, :])

        protein_ligand_edge_attr = edge_attr[protein_ligand_edge_attr_idx, :]

        protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)

        return graph, protein_graph, ligand_graph, protein_ligand_graph

    def get(self, idx):
        """
        根据索引获取一个批次的数据。
        此函数的行为在训练和验证模式下有很大不同。

        参数:
            idx (int): 数据集的索引。

        返回:
            tuple: 包含四个批次对象的元组，分别对应复合物图、蛋白质图、配体图和相互作用图。
        """

        pdb_id_graphs = []
        iter_graph_paths = []
        if self.train_mode:
            iter_graph_paths.extend(self.path_dict_list[idx]["native"])
            ########### sampling ratio
            for random_i in random.sample(range(0, self.len_dataset), 4):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["dock"], 1))
            for random_i in random.sample(range(0, self.len_dataset), 2):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["cross"], 1))
            for random_i in random.sample(range(0, self.len_dataset), 2):
                iter_graph_paths.extend(random.sample(self.path_dict_list[random_i]["random"], 1))


            for iter_graph_path in iter_graph_paths:
                with open(iter_graph_path, 'rb') as f:
                    pdb_id_graph = pickle.load(f)
                pdb_id_graphs.append(pdb_id_graph)

        else:

            iter_graph_paths.extend(self.path_dict_list[idx]["native"])
            iter_graph_paths.extend(self.path_dict_list[idx]["dock"])
            iter_graph_paths.extend(self.path_dict_list[idx]["cross"])
            iter_graph_paths.extend(self.path_dict_list[idx]["random"])

            for iter_graph_path in iter_graph_paths:
                with open(iter_graph_path, 'rb') as f:
                    pdb_id_graph = pickle.load(f)
                pdb_id_graphs.append(pdb_id_graph)

        graph_list = []
        protein_graph_list = []
        ligand_graph_list = []
        protein_ligand_graph_list = []
        for graph in pdb_id_graphs:
            graph = self.graph_rmsd_bindaff_aug(graph)
            graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)

            graph_list.append(graph)
            protein_graph_list.append(protein_graph)
            ligand_graph_list.append(ligand_graph)
            protein_ligand_graph_list.append(protein_ligand_graph)

        graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
        protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
        ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
        protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)

        return graph_list_batch, protein_graph_list_batch, ligand_graph_list_batch, protein_ligand_graph_list_batch




def get_data_type(name_list):
    """
    根据文件名解析一个批次中每个图的类型。
    文件名中包含一个类型指示符: '_0_' for native, '_1_' for dock, '_2_' for cross, '_3_' for random。
    这个函数对于在计算损失时区分不同类型的样本至关重要。

    参数:
        name_list (list[str]): 一个批次中所有图的名称列表。

    返回:
        dict: 一个字典，键为类型("native", "dock", "cross", "random")，
              值为对应类型图在批次中的索引列表。
    """
    data_type_dict_batch = {
        "native": [],
        "dock": [],
        "cross": [],
        "random": [],
    }
    name_list = list(chain(*name_list))
    for name_i, name in enumerate(name_list):
        name_splited = name.split('_')
        if name_splited[-2] == '0':
            data_type_dict_batch["native"].append(name_i)
        elif name_splited[-2] == '1':
            data_type_dict_batch["dock"].append(name_i)
        elif name_splited[-2] == '2':
            data_type_dict_batch["cross"].append(name_i)
        elif name_splited[-2] == '3':
            data_type_dict_batch["random"].append(name_i)
    return data_type_dict_batch


def valid_model_epoch(model, loader, criterion, cr_loss_weight=5.0):
    """
    在验证集上评估模型一个周期的性能。

    参数:
        model (torch.nn.Module): 待评估的模型。
        loader (torch.utils.data.DataLoader): 验证数据加载器。
        criterion (dict): 包含损失函数的字典 (例如 'mse': MSELoss)。

    返回:
        dict: 包含该周期内各种平均损失和评估指标的字典。
    """
    model.eval()
    epoch_loss = {
        "total": 0.0,
        "bind_native": 0.0,
        "bind_cross_random": 0.0,
        "rmsd": 0.0,
        "suc_top1": 0.0,
        "suc_top5": 0.0,
    }


    success_count = {
        "top1": 0.0,
        "top5": 0.0,
    }

    target_count = 0
    with torch.no_grad():

        for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):

            data_type_dict_batch = get_data_type(graph_batch.name)

            bind_logits, rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE),
                                             ligand_graph_batch.to(DEVICE),
                                             protein_ligand_graph_batch.to(DEVICE))

            ### binding affinity mse loss
            loss_bind_native_mse = criterion['mse'](bind_logits[data_type_dict_batch["native"]],
                                                    graph_batch.bind[data_type_dict_batch["native"]])
            loss_bind_cross_random = -5 - bind_logits[data_type_dict_batch["cross"] + data_type_dict_batch["random"]]
            loss_bind_cross_random = loss_bind_cross_random.clamp(min=0.0).mean() * cr_loss_weight  ### add weight

            ### rmsd mse loss
            loss_rmsd = criterion['mse'](rmsd_logits[data_type_dict_batch["native"] + data_type_dict_batch["dock"]],
                                         graph_batch.y[data_type_dict_batch["native"] + data_type_dict_batch["dock"]])

            total_loss = loss_bind_native_mse + loss_bind_cross_random + loss_rmsd

            print(f"Validatoin: {idx}/{len(loader)}, total: {total_loss:.4f}, bind native: {loss_bind_native_mse:.4f}, "
                  f"bind cross random: {loss_bind_cross_random:.4f}, rmsd: {loss_rmsd:.4f},")
            epoch_loss["total"] += total_loss.item()
            epoch_loss["bind_native"] += loss_bind_native_mse.item()
            epoch_loss["bind_cross_random"] += loss_bind_cross_random.item()
            epoch_loss["rmsd"] += loss_rmsd.item()

            preds = bind_logits + rmsd_logits
            native_index = data_type_dict_batch["native"][0]
            #### success rate in top n
            top_list = torch.topk(preds, 10, dim=0, largest=False).indices.squeeze().tolist()
            target_count = target_count + 1
            if native_index in top_list:
                success_count["top5"] += 1
            if native_index in top_list[:3]:
                success_count["top1"] += 1

    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key] / len(loader)

    epoch_loss["suc_top1"] = success_count["top1"] / target_count
    epoch_loss["suc_top5"] = success_count["top5"] / target_count

    print(f'top1 {success_count["top1"] / target_count}')
    print(f'top5 {success_count["top5"] / target_count}')


    return epoch_loss


def train_model_epoch(model, loader, criterion, optimizer, cr_loss_weight=5.0):
    """
    在训练集上训练模型一个周期。

    参数:
        model (torch.nn.Module): 待训练的模型。
        loader (torch.utils.data.DataLoader): 训练数据加载器。
        criterion (dict): 包含损失函数的字典。
        optimizer (torch.optim.Optimizer): 用于更新模型参数的优化器。

    返回:
        tuple:
            - model (torch.nn.Module): 训练一个周期后的模型。
            - dict: 包含该周期内各种平均训练损失的字典。
    """
    model.train()
    epoch_loss = {
        "total": 0.0,
        "bind_native": 0.0,
        "bind_cross_random": 0.0,
        "rmsd": 0.0,
    }

    for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):

        data_type_dict_batch = get_data_type(graph_batch.name)



        bind_logits, rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE), ligand_graph_batch.to(DEVICE),
                            protein_ligand_graph_batch.to(DEVICE))


        ### binding affinity mse loss
        loss_bind_native_mse = criterion['mse'](bind_logits[data_type_dict_batch["native"]], graph_batch.bind[data_type_dict_batch["native"]])
        loss_bind_cross_random = -5 - bind_logits[data_type_dict_batch["cross"] + data_type_dict_batch["random"]]
        loss_bind_cross_random = loss_bind_cross_random.clamp(min=0.0).mean() * cr_loss_weight ### add weight

        ### rmsd mse loss
        loss_rmsd = criterion['mse'](rmsd_logits[data_type_dict_batch["native"] + data_type_dict_batch["dock"]],
                                     graph_batch.y[data_type_dict_batch["native"] + data_type_dict_batch["dock"]])


        total_loss = loss_bind_native_mse + loss_bind_cross_random + loss_rmsd
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()

        epoch_loss["total"] += total_loss.item()
        epoch_loss["bind_native"] += loss_bind_native_mse.item()
        epoch_loss["bind_cross_random"] += loss_bind_cross_random.item()
        epoch_loss["rmsd"] += loss_rmsd.item()

        print(f"Train: {idx}/{len(loader)}, total: {total_loss:.4f}, bind native: {loss_bind_native_mse:.4f}, "
              f"bind cross random: {loss_bind_cross_random:.4f}, rmsd: {loss_rmsd:.4f},")


    for key in epoch_loss.keys():
        epoch_loss[key] = epoch_loss[key] / len(loader)

    return model, epoch_loss


if __name__ == '__main__':
    """
    主执行模块。
    当脚本作为主程序运行时，此部分代码将被执行。
    它负责处理命令行参数，初始化数据集、数据加载器、模型、损失函数和优化器，
    并启动整个训练和验证循环。
    """
    parser = argparse.ArgumentParser(description='Graph based Protein-Ligand Binding Affinity Regression')
    parser.add_argument('--data_dir', default=r'', type=str, help='data directory')
    parser.add_argument('--train_list_path', default=r'', type=str, help='train list path')
    parser.add_argument('--valid_list_path', default=r'', type=str, help='validation list path')

    parser.add_argument('--exclude_list_path', default=r'', type=str, help='exclude list path')

    parser.add_argument('--model_save_dir', default=r'', type=str, help='model save directory')
    parser.add_argument('--resume_model_path', default=r'', type=str, help='resume model path')
    parser.add_argument('--num_workers', default=8, type=int, help="cpu worker number")
    parser.add_argument('--batch_size', default=4, type=int, help='batch size')

    parser.add_argument('--lr', default=0.0001, type=float, help='Learnig Rate')
    parser.add_argument('--node_dim', default=73, type=int, help="Input size of layer's node")
    parser.add_argument('--edge_dim', default=24, type=int, help='edge dimension')


    parser.add_argument('--hidden_dim', default=128, type=int, help='Hidden layer size')
    parser.add_argument('--num_layers', default=5, type=int, help='number of gnn layer')
    parser.add_argument('--dropout', default=0.25, type=float, help='dropout ratio')
    parser.add_argument('--cut_edge_dis', default=8, type=int, help='non-covalent bond edge distance')



    parser.add_argument('--rmsd_cutoff', default=2, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_aug_upper', default=1, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_aug_lower', default=-1, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_rej_upper', default=0, type=int, help='number of gnn layer')
    parser.add_argument('--bind_aff_rej_lower', default=-5, type=int, help='number of gnn layer')

    parser.add_argument('--max-epochs', default=5000, type=int, help='maximum number of training epochs')
    parser.add_argument('--cr-loss-weight', default=5.0, type=float, help='weight coefficient for cross/random bind energy loss')

    args = parser.parse_args()
    print(args)
    model_save_dir = os.path.join(args.model_save_dir, f"lr{args.lr}_bs{args.batch_size}_nd{args.node_dim}_ed{args.edge_dim}"
                                                       f"_hd{args.hidden_dim}_nl{args.num_layers}_do{args.dropout}_ced{args.cut_edge_dis}")

    train_dataset = akscore2_dataset(args.data_dir, args.train_list_path,
                                     exclude_list_path=args.exclude_list_path,
                                     train_mode=True,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])
    valid_dataset = akscore2_dataset(args.data_dir, args.valid_list_path,
                                     exclude_list_path=args.exclude_list_path,
                                     train_mode=False,
                                     rmsd_cutoff=args.rmsd_cutoff,
                                     bind_aff_aug=[args.bind_aff_aug_lower, args.bind_aff_aug_upper],
                                     bind_aff_rej=[args.bind_aff_rej_lower, args.bind_aff_rej_upper])

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers)
    val_loader = DataLoader(valid_dataset, batch_size=1, shuffle=False, num_workers=args.num_workers)

    print(f"train len: {len(train_loader)}, val len: {len(val_loader)},")

    loader = {
        'train_loader': train_loader,
        'val_loader': val_loader,
    }

    model = GATv2(args.node_dim, args.edge_dim, num_layers=args.num_layers, hidden_dim=args.hidden_dim,
                  dropout=args.dropout, cut_edge_dis=args.cut_edge_dis).to(DEVICE)

    criterion = {
        'mse': torch.nn.MSELoss(),
        'bce': torch.nn.BCEWithLogitsLoss(),
    }

    #     optimizer = torch.optim.RAdam(model.parameters(), lr=args.lr)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)

    ### resume model training
    resume_epoch = 1
    if os.path.isfile(args.resume_model_path):
        ch = torch.load(args.resume_model_path)
        model.load_state_dict(ch['model'])
        model.to(DEVICE)
        resume_epoch = ch['epoch'] + 1
        optimizer.load_state_dict(ch['optimizer'])
#         model_save_dir = model_save_dir + f"_resume_lr{args.lr}"
        for g in optimizer.param_groups:
            g['lr'] = args.lr
        print(f"model loaded: {args.resume_model_path}")

    if not os.path.exists(model_save_dir):
        os.makedirs(model_save_dir)


    for epoch in range(resume_epoch, args.max_epochs):
        print("=" * 120)
        print(f"Epoch {epoch} Start:")

        model, avg_epoch_train_loss = train_model_epoch(model, loader['train_loader'], criterion, optimizer, args.cr_loss_weight)
        time.sleep(2)

        print("\n\n")
        print_txt_train = f'Epoch: {epoch} \t train_total: {avg_epoch_train_loss["total"]:.4f} \t train_bind_n: {avg_epoch_train_loss["bind_native"]:.4f} \t ' \
                          f'train_bind_cr {avg_epoch_train_loss["bind_cross_random"]:.4f} \t train_rmsd: {avg_epoch_train_loss["rmsd"]:.4f} \t '
        print("Train")
        print(print_txt_train)


        if epoch % 10 == 0:
            avg_epoch_val_loss = valid_model_epoch(model, loader['val_loader'], criterion, args.cr_loss_weight)
            time.sleep(2)
            print_txt_valid = f'Epoch: {epoch} \t valid_total: {avg_epoch_val_loss["total"]:.4f} \t valid_bind_n: {avg_epoch_val_loss["bind_native"]:.4f} \t ' \
                              f'valid_bind_cr {avg_epoch_val_loss["bind_cross_random"]:.4f} \t valid_rmsd: {avg_epoch_val_loss["rmsd"]:.4f} \t ' \
                              f'valid_top1: {avg_epoch_val_loss["suc_top1"]} \t valid_top5: {avg_epoch_val_loss["suc_top5"]} \t '
            print("Valid")
            print(print_txt_valid)

            obj = {
                'epoch': epoch,
                'model': model.state_dict(),
                'optimizer': optimizer.state_dict(),
            }
            checkpoint_filename = f"checkpoint_{epoch}_tl_{avg_epoch_train_loss['total']:.3f}_vl_{avg_epoch_val_loss['total']:.3f}_" \
                                  f"vs1_{avg_epoch_val_loss['suc_top1']:.3f}_vs5_{avg_epoch_val_loss['suc_top5']:.3f}.pth"
            torch.save(obj, os.path.join(model_save_dir, checkpoint_filename))

            time.sleep(5)

            fconv = open(os.path.join(model_save_dir, 'train_val_log.tsv'), 'a')
            fconv.write(print_txt_train + print_txt_valid + '\n')
            fconv.close()






