# test_akscore2_v8_dude_screen_pred.py 脚本技术文档

## 1. 脚本运行逻辑分析

### 1.1 主要执行流程

脚本的执行流程可以分为四个主要阶段：

#### 阶段1：初始化和模型加载（第232-271行）
1. **参数解析**：解析命令行参数，包括数据路径、模型路径、网络参数等
2. **模型初始化**：创建GATv2模型实例，设置网络架构参数
3. **模型加载**：从checkpoint文件加载预训练模型权重
4. **路径设置**：确定结果保存目录

#### 阶段2：数据集准备（第273-303行）
1. **目标蛋白确定**：根据`target_name`参数确定要处理的蛋白质
2. **数据路径构建**：构建数据文件路径 `{screen_dir}/{target_name}/glide_graph_one/`
3. **数据集创建**：实例化`akscore2_dataset`对象
4. **数据加载器创建**：配置DataLoader进行批量数据处理

#### 阶段3：批量预测（第305-315行）
1. **批量数据加载**：使用DataLoader按批次加载`.pkl.gz`文件
2. **图数据处理**：对每个文件中的图数据进行分解和预处理
3. **模型推理**：通过GATv2模型进行结合亲和力预测
4. **结果收集**：收集预测分数和对应的分子名称

#### 阶段4：结果保存（第316行）
调用`save_result`函数将预测结果保存到指定格式的文件中

### 1.2 关键函数调用链

```
main() 
├── GATv2() - 模型初始化
├── torch.load() - 模型权重加载
├── akscore2_dataset() - 数据集创建
│   ├── __init__() - 扫描.pkl.gz文件
│   └── get() - 单个文件处理
│       ├── gzip.open() + pickle.load() - 文件解压和加载
│       ├── graph_modification() - 图数据分解
│       └── pyg_batch_func.Batch.from_data_list() - 批次构建
├── DataLoader() - 数据加载器创建
├── model.forward() - 模型推理
│   ├── node_to_hidden() - 节点特征转换
│   ├── 边距离过滤 - 根据cut_edge_dis参数
│   ├── GATv2Conv层 - 图注意力网络
│   ├── global_mean_pool() - 全局池化
│   └── graph_dec_bind() - 结合亲和力预测头
└── save_result() - 结果保存
```

### 1.3 核心算法和业务逻辑

#### 图数据分解算法（graph_modification函数）
脚本的核心业务逻辑是将蛋白质-配体复合物图分解为四种不同的图表示：

1. **边类型识别**：根据边特征的前3个元素识别边类型
   - `[1,0,0]`：蛋白质内部边（共价键和氢键）
   - `[0,1,0]`：配体内部边（分子内键合）
   - `[0,0,1]`：蛋白质-配体交互边（非共价相互作用）

2. **节点分离**：通过配体边的最小索引确定蛋白质和配体节点的分界点

3. **子图构建**：
   - **原始图**：保持完整的复合物结构
   - **蛋白质图**：仅包含蛋白质节点和内部边
   - **配体图**：仅包含配体节点和内部边（重新索引）
   - **交互图**：包含所有节点但仅保留蛋白质-配体交互边

#### GATv2模型预测流程
1. **节点特征映射**：将73维原子特征映射到256维隐藏空间
2. **边距离过滤**：根据`cut_edge_dis`参数过滤远距离非共价相互作用
3. **图注意力学习**：通过5层GATv2Conv学习原子间相互作用
4. **全局特征聚合**：使用全局平均池化聚合分子级特征
5. **结合亲和力预测**：通过3层MLP输出结合亲和力分数

## 2. 输入文件结构详解

### 2.1 目录结构要求

脚本要求严格的目录层次结构：

```
{screen_dir}/
├── {target_name}/
│   └── glide_graph_one/
│       ├── compound_1.pkl.gz
│       ├── compound_2.pkl.gz
│       ├── compound_3.pkl.gz
│       └── ...
└── other_targets/
    └── glide_graph_one/
        └── ...
```

#### 路径构建逻辑
- **基础路径**：`target_dir = os.path.join(args.screen_dir, target_name)`
- **数据路径**：`target_dir = os.path.join(target_dir, "glide_graph_one")`

#### 示例路径
- `--screen_dir="/data/DUD_E"`
- `--target_name="aa2ar"`  
- 最终数据路径：`/data/DUD_E/aa2ar/glide_graph_one/`

### 2.2 .pkl.gz文件内部数据结构

#### 文件格式
每个`.pkl.gz`文件是gzip压缩的Python pickle文件，包含一个图对象列表：

```python
# 文件加载过程（第163-164行）
with gzip.open(graph_path, 'rb') as f:
    pdb_id_graphs = pickle.load(f)  # 返回图对象列表
```

#### 数据内容
`pdb_id_graphs`是一个列表，每个元素是PyTorch Geometric的`Data`对象，包含：

**1. 节点特征 (x)**：形状为`[num_nodes, 73]`的张量
- **维度构成**：Symbol[20] + Degree[7] + NumHs[5] + ImpVal[6] + ExpVal[6] + ExpHs[2] + ImpHs[5] + Hybri[5] + Arom[1] + IsRing[1] + Radical[3] + FormalCharge[7] + Hydropho[1] + donor[1] + acceptor[1] + acidic[1] + basic[1] = 73
- **节点类型**：前N个节点为蛋白质原子，后M个节点为配体原子
- **特征含义**：
  - 原子符号的one-hot编码
  - 原子的化学性质（电荷、杂化、芳香性等）
  - 蛋白质原子的疏水性、氢键供受体性质

**2. 边索引 (edge_index)**：形状为`[2, num_edges]`的张量
- 定义原子间的连接关系
- 包含三种类型的边：蛋白质内部边、配体内部边、蛋白质-配体交互边

**3. 边特征 (edge_attr)**：形状为`[num_edges, 24]`的张量
- **维度构成**：PLbond[3] + bondtype[4] + Stereo[6] + Ring[1] + conjugate[1] + PLbondtype[5] + bond_distance[4] = 24
- **前3个元素**：边类型标识符
  - `[1,0,0]`：蛋白质内部边
  - `[0,1,0]`：配体内部边  
  - `[0,0,1]`：蛋白质-配体交互边
- **其他特征**：键类型、立体化学、环状性、共轭性、距离信息

**4. 分子标识 (name)**：字符串，通常从文件名提取

#### 数据示例结构
```python
# 单个图对象的结构
graph = Data(
    x=torch.tensor([[...], [...], ...]),      # [num_atoms, 73]
    edge_index=torch.tensor([[...], [...]]),  # [2, num_bonds]
    edge_attr=torch.tensor([[...], [...], ...]), # [num_bonds, 24]
    name="compound_001"
)
```

### 2.3 其他输入文件

#### 模型文件
- **格式**：PyTorch checkpoint文件（.pth）
- **内容**：包含模型状态字典和训练参数
- **加载方式**：`torch.load(args.model_path)`

#### 配置参数
脚本通过命令行参数接收配置，无需额外配置文件

## 3. 输出文件结构详解

### 3.1 输出目录结构

```
{result_save_dir}/
└── glide_sp_bind/
    └── {target_name}.txt
```

### 3.2 输出文件格式

#### 文件命名规则
- **目录名**：`glide_sp_bind`（固定）
- **文件名**：`{target_name}.txt`

#### 文件内容格式
每行包含三个制表符分隔的字段：

```
{processed_name}    {index}    {binding_score}
```

**字段说明**：
1. **processed_name**：处理后的分子名称
   - 原始名称格式：`prefix_middle_suffix`
   - 处理逻辑：保留第一部分和最后一部分，格式为`prefix_suffix`
   - 代码：`name.split("_")[0] + "_" + name.split("_")[-1]`

2. **index**：分子在结果列表中的索引（从0开始）

3. **binding_score**：模型预测的结合亲和力分数（保留4位小数）

#### 输出示例
```
CHEMBL123_001    0    -8.2341
CHEMBL456_002    1    -7.8956
CHEMBL789_003    2    -7.5432
```

### 3.3 结果数据组织

#### 内存中的结果结构
```python
result = {
    "bind_preds": [score1, score2, ...],     # 结合亲和力预测列表
    "rmsd_preds": [],                        # 空列表（v8版本未使用）
    "bind_rmsd_preds": [],                   # 空列表（v8版本未使用）
    "names": [name1, name2, ...],            # 分子名称列表
    "pdb_id": target_name,                   # 目标蛋白名称
}
```

#### 数据收集过程
1. **批量预测**：每个批次的预测结果通过`extend`方法添加到列表
2. **名称提取**：从图批次对象中提取分子名称
3. **数据对应**：确保预测分数和分子名称的一一对应关系

## 4. 运行参数影响分析

### 4.1 路径相关参数

#### --screen_dir（数据根目录）
- **作用**：指定包含所有目标蛋白数据的根目录
- **影响**：直接决定数据文件的查找路径
- **要求**：目录下必须包含以目标蛋白名称命名的子目录

#### --target_name（目标蛋白名称）
- **作用**：指定要处理的特定目标蛋白
- **影响**：
  - 确定数据子目录：`{screen_dir}/{target_name}/glide_graph_one/`
  - 决定输出文件名：`{target_name}.txt`
- **格式**：通常为PDB ID或蛋白质代码

#### --result_save_dir（结果保存目录）
- **作用**：指定预测结果的保存位置
- **默认行为**：如果未指定，使用模型文件路径（去除扩展名）
- **影响**：决定输出文件的完整路径

#### --model_path（模型文件路径）
- **作用**：指定预训练模型的checkpoint文件
- **要求**：文件必须包含兼容的模型状态字典
- **影响**：直接影响预测结果的质量和准确性

### 4.2 模型架构参数

#### 网络结构参数
- **--node_dim**（默认73）：节点特征维度，必须与输入数据匹配
- **--edge_dim**（默认24）：边特征维度，必须与输入数据匹配  
- **--hidden_dim**（默认256）：隐藏层维度，影响模型容量和性能
- **--num_layers**（默认5）：GATv2层数，影响模型深度和表达能力
- **--dropout**（默认0.25）：dropout比率，影响模型泛化能力

#### --cut_edge_dis（边距离阈值）
- **默认值**：8（保留所有边）
- **可选值**：
  - 8：保留所有非共价相互作用边
  - 6：仅保留6Å内的相互作用
  - 4：仅保留4Å内的相互作用
- **影响**：
  - 较小值：减少远程相互作用，可能提高计算效率但损失信息
  - 较大值：保留更多相互作用信息，但增加计算复杂度

### 4.3 数据处理参数

#### --batch_size（批次大小）
- **默认值**：4
- **影响**：
  - 较大值：提高GPU利用率，但需要更多内存
  - 较小值：降低内存需求，但可能影响处理效率
- **建议**：根据GPU内存和数据复杂度调整

#### --num_workers（CPU工作进程数）
- **默认值**：4
- **影响**：
  - 影响数据加载的并行度
  - 过多可能导致内存压力
  - 过少可能成为性能瓶颈

### 4.4 参数组合对脚本行为的影响

#### 高性能配置
```bash
--batch_size 8 --num_workers 8 --hidden_dim 512 --cut_edge_dis 8
```
- 适用于高端GPU和充足内存环境
- 最大化模型性能和处理速度

#### 内存优化配置  
```bash
--batch_size 2 --num_workers 2 --hidden_dim 128 --cut_edge_dis 6
```
- 适用于内存受限环境
- 在性能和资源消耗间平衡

#### 快速测试配置
```bash
--batch_size 1 --num_workers 1 --cut_edge_dis 4
```
- 适用于快速验证和调试
- 最小化资源需求

### 4.5 参数验证和错误处理

脚本对关键参数进行验证：
1. **模型文件存在性**：`os.path.isfile(args.model_path)`
2. **目录创建**：自动创建不存在的结果保存目录
3. **数据文件扫描**：自动发现指定目录下的所有`.pkl.gz`文件

这些参数的合理配置直接影响脚本的执行效率、内存使用和预测质量，需要根据具体的硬件环境和数据特点进行调整。

## 5. 数据处理管道深度解析

### 5.1 图数据加载和预处理流程

#### akscore2_dataset类详解

<augment_code_snippet path="test_akscore2_v8_dude_screen_pred.py" mode="EXCERPT">
````python
class akscore2_dataset(Dataset):
    def __init__(self, data_dir):
        super(akscore2_dataset, self).__init__()
        self.data_dir = data_dir
        self.graph_paths = []
        self.graph_paths = [os.path.join(self.data_dir, f) for f in os.listdir(self.data_dir) if f.endswith(".pkl.gz")]
````
</augment_code_snippet>

**初始化过程**：
1. 扫描指定目录下的所有`.pkl.gz`文件
2. 构建完整的文件路径列表
3. 文件数量决定数据集大小

#### 单文件处理流程（get方法）

<augment_code_snippet path="test_akscore2_v8_dude_screen_pred.py" mode="EXCERPT">
````python
def get(self, idx):
    graph_path = self.graph_paths[idx]
    with gzip.open(graph_path, 'rb') as f:
        pdb_id_graphs = pickle.load(f)

    graph_list = []
    protein_graph_list = []
    ligand_graph_list = []
    protein_ligand_graph_list = []

    for graph in pdb_id_graphs:
        graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)
        graph.name = os.path.split(graph_path)[-1].split(".pkl.gz")[0]

        graph_list.append(graph)
        protein_graph_list.append(protein_graph)
        ligand_graph_list.append(ligand_graph)
        protein_ligand_graph_list.append(protein_ligand_graph)
````
</augment_code_snippet>

**处理步骤**：
1. **文件解压**：使用gzip解压缩文件
2. **数据反序列化**：使用pickle加载Python对象
3. **图分解**：调用graph_modification函数
4. **名称设置**：从文件名提取分子标识符
5. **批次构建**：将四种图类型分别打包

### 5.2 图分解算法详解

#### graph_modification函数核心逻辑

<augment_code_snippet path="test_akscore2_v8_dude_screen_pred.py" mode="EXCERPT">
````python
def graph_modification(self, graph):
    x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()

    protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
    ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
    protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]
````
</augment_code_snippet>

**边类型识别机制**：
- 通过边特征的前3个元素进行分类
- 使用张量比较和逻辑操作快速筛选
- 分别获取三种边类型的索引

#### 节点和边的重新组织

**蛋白质图构建**：
```python
protein_x = x[:protein_ligand_node_sep_idx, :]
protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx, :])
```

**配体图构建**：
```python
ligand_x = x[protein_ligand_node_sep_idx:, :]
ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index - torch.min(ligand_edge_index), edge_attr=edge_attr[ligand_edge_attr_idx, :])
```

**关键技术点**：
1. **节点分离**：通过配体边的最小索引确定分界点
2. **索引重映射**：配体图的边索引需要重新从0开始编号
3. **特征保持**：保持原始的节点和边特征不变

### 5.3 模型推理过程详解

#### GATv2网络架构

<augment_code_snippet path="test_akscore2_v8_dude_screen_pred.py" mode="EXCERPT">
````python
class GATv2(torch.nn.Module):
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64, dropout=0.25, cut_edge_dis=8):
        super().__init__()
        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(dropout)

        self.protein_ligand_gnn_layers = []
        for num in range(self.num_layers):
            self.protein_ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_ligand_gnn_layers = nn.ModuleList(self.protein_ligand_gnn_layers)
````
</augment_code_snippet>

**网络组件**：
1. **特征映射层**：将73维原子特征映射到隐藏维度
2. **图注意力层**：5层GATv2Conv进行特征学习
3. **预测头**：3层MLP输出结合亲和力

#### 边距离过滤机制

<augment_code_snippet path="test_akscore2_v8_dude_screen_pred.py" mode="EXCERPT">
````python
if self.cut_edge_dis == 6:
    edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-1] != torch.Tensor([0, 0, 0]).to(DEVICE)).any(dim=1))[0]
    edge_index = edge_index[:, edge_attr_idx_filtered]
    edge_attr = edge_attr[edge_attr_idx_filtered, :]
elif self.cut_edge_dis == 4:
    edge_attr_idx_filtered = torch.where((edge_attr[:, -4:-2] != torch.Tensor([0, 0]).to(DEVICE)).any(dim=1))[0]
    edge_index = edge_index[:, edge_attr_idx_filtered]
    edge_attr = edge_attr[edge_attr_idx_filtered, :]
````
</augment_code_snippet>

**过滤逻辑**：
- 基于边特征的距离编码进行过滤
- 不同阈值对应不同的距离范围
- 动态调整图的连接密度

## 6. 性能优化和最佳实践

### 6.1 内存管理策略

#### 数据加载优化
- **延迟加载**：数据集采用按需加载策略，避免一次性加载所有数据
- **内存释放**：使用`detach().clone()`确保梯度计算的内存安全
- **批次处理**：通过合理的batch_size平衡内存使用和计算效率

#### GPU内存优化
```python
with torch.no_grad():  # 禁用梯度计算
    for idx, (graph_batch, ...) in enumerate(loader):
        bind_logits = model(graph_batch.to(DEVICE), ...)
```

### 6.2 计算效率优化

#### 并行处理
- **数据加载并行**：通过num_workers参数启用多进程数据加载
- **GPU加速**：自动检测CUDA可用性并使用GPU加速
- **批次向量化**：利用PyTorch Geometric的批次处理能力

#### 模型推理优化
- **模型评估模式**：使用`model.eval()`禁用dropout和batch normalization
- **无梯度计算**：使用`torch.no_grad()`减少内存占用
- **设备优化**：数据和模型统一放置在同一设备上

### 6.3 错误处理和鲁棒性

#### 文件处理错误
- **文件存在性检查**：验证模型文件和数据目录的存在性
- **格式验证**：确保输入文件为正确的.pkl.gz格式
- **权限检查**：验证文件读写权限

#### 数据完整性检查
- **图数据验证**：检查节点和边的维度匹配
- **批次一致性**：确保批次内所有图的特征维度一致
- **设备兼容性**：处理CPU/GPU设备转换问题

## 7. 与其他版本的对比分析

### 7.1 与v7版本的主要区别

#### 模型架构差异
- **v7版本**：双输出头设计（结合亲和力 + RMSD预测）
- **v8版本**：单输出头设计（仅结合亲和力预测）

#### 预测逻辑差异
```python
# v7版本
bind_logits, rmsd_logits = model(...)
final_score = bind_logits + rmsd_logits

# v8版本
bind_logits = model(...)
final_score = bind_logits
```

#### 标签增强技术
v8版本通过标签增强技术将RMSD信息隐式编码到结合亲和力标签中，实现了单任务学习的简化设计。

### 7.2 与v4版本的主要区别

#### 学习范式
- **v4版本**：二分类学习（好构象 vs 差构象）
- **v8版本**：回归学习（连续的结合亲和力值）

#### 输出处理
- **v4版本**：使用sigmoid激活函数输出概率
- **v8版本**：直接输出回归值

### 7.3 版本演进总结

| 特性 | v4版本 | v7版本 | v8版本 |
|------|--------|--------|--------|
| 学习任务 | 二分类 | 多任务回归 | 单任务回归 |
| 输出头数量 | 1个 | 2个 | 1个 |
| 标签类型 | 二值标签 | 双重标签 | 增强标签 |
| 模型复杂度 | 低 | 高 | 中等 |
| 训练稳定性 | 中等 | 较低 | 较高 |

v8版本在保持模型简洁性的同时，通过巧妙的标签增强技术实现了对构象质量的有效感知，代表了该系列模型的最新发展方向。

## 8. 数据结构具体示例

### 8.1 节点特征详细解析

#### 73维节点特征构成
基于代码注释中的特征定义：

```python
# node_attr = Symbol[20] + Degree[7] + NumHs[5] + ImpVal[6] + ExpVal[6] + ExpHs[2] + ImpHs[5] + Hybri[5] + Arom[1] + IsRing[1] + Radical[3] + FormalCharge[7] + Hydropho[1] + donor[1] + acceptor[1] + acidic[1] + basic[1] = [73]
```

**特征分组详解**：

1. **原子符号 (Symbol[20])**：
   - 常见原子的one-hot编码：C, N, O, S, P, F, Cl, Br, I等
   - 覆盖蛋白质和小分子中的主要原子类型

2. **连接度信息 (Degree[7] + NumHs[5])**：
   - Degree[7]：原子的连接度（0-6及以上）
   - NumHs[5]：氢原子数量（0-4及以上）

3. **价态信息 (ImpVal[6] + ExpVal[6] + ExpHs[2] + ImpHs[5])**：
   - ImpVal[6]：隐式价态
   - ExpVal[6]：显式价态
   - ExpHs[2]：显式氢原子数
   - ImpHs[5]：隐式氢原子数

4. **化学性质 (Hybri[5] + Arom[1] + IsRing[1] + Radical[3] + FormalCharge[7])**：
   - Hybri[5]：杂化类型（sp, sp2, sp3等）
   - Arom[1]：芳香性（是/否）
   - IsRing[1]：是否在环中
   - Radical[3]：自由基电子数
   - FormalCharge[7]：形式电荷（-3到+3）

5. **生物化学性质 (Hydropho[1] + donor[1] + acceptor[1] + acidic[1] + basic[1])**：
   - Hydropho[1]：疏水性
   - donor[1]：氢键供体
   - acceptor[1]：氢键受体
   - acidic[1]：酸性
   - basic[1]：碱性

#### 节点特征示例
```python
# 蛋白质中的苯丙氨酸侧链碳原子
phe_carbon = [
    # Symbol[20]: 碳原子
    [0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
    # Degree[7]: 连接度为3
    [0,0,0,1,0,0,0],
    # NumHs[5]: 1个氢原子
    [0,1,0,0,0],
    # ... 其他特征
    # Hydropho[1]: 疏水性为1
    [1],
    # donor[1]: 非氢键供体
    [0],
    # acceptor[1]: 非氢键受体
    [0],
    # acidic[1]: 非酸性
    [0],
    # basic[1]: 非碱性
    [0]
]
```

### 8.2 边特征详细解析

#### 24维边特征构成
```python
# edge_attr = PLbond[3] + bondtype[4] + Stereo[6] + Ring[1] + conjugate[1] + PLbondtype[5] + bond_distance[4] = [24]
```

**特征分组详解**：

1. **蛋白质-配体键类型 (PLbond[3])**：
   - `[1,0,0]`：蛋白质内部键
   - `[0,1,0]`：配体内部键
   - `[0,0,1]`：蛋白质-配体间相互作用

2. **化学键类型 (bondtype[4])**：
   - 单键、双键、三键、芳香键的one-hot编码

3. **立体化学 (Stereo[6])**：
   - 键的立体化学信息（E/Z构型等）

4. **环状性和共轭性 (Ring[1] + conjugate[1])**：
   - Ring[1]：是否为环内键
   - conjugate[1]：是否为共轭键

5. **相互作用类型 (PLbondtype[5])**：
   - 氢键、疏水相互作用、静电相互作用等

6. **距离信息 (bond_distance[4])**：
   - 原子间距离的分箱编码（如2-4Å, 4-6Å, 6-8Å, >8Å）

#### 边特征示例
```python
# 蛋白质-配体氢键
hydrogen_bond = [
    # PLbond[3]: 蛋白质-配体间相互作用
    [0,0,1],
    # bondtype[4]: 非共价键
    [0,0,0,1],
    # Stereo[6]: 无立体化学
    [0,0,0,0,0,0],
    # Ring[1]: 非环内键
    [0],
    # conjugate[1]: 非共轭键
    [0],
    # PLbondtype[5]: 氢键类型
    [1,0,0,0,0],
    # bond_distance[4]: 2-4Å距离范围
    [1,0,0,0]
]
```

### 8.3 完整数据流示例

#### 输入文件示例结构
```python
# compound_001.pkl.gz 文件内容
pdb_id_graphs = [
    Data(
        x=torch.tensor([
            # 蛋白质原子特征 (前N行)
            [1,0,0,...,1,0,1,0,0],  # 蛋白质原子1
            [0,1,0,...,0,1,0,1,0],  # 蛋白质原子2
            # ...
            # 配体原子特征 (后M行)
            [0,0,1,...,1,1,0,0,1],  # 配体原子1
            [1,0,0,...,0,0,1,0,0],  # 配体原子2
        ], dtype=torch.float),

        edge_index=torch.tensor([
            [0,1,2,3,...],  # 源节点索引
            [1,2,3,4,...]   # 目标节点索引
        ], dtype=torch.long),

        edge_attr=torch.tensor([
            [1,0,0,1,0,0,0,...],  # 蛋白质内部边
            [0,1,0,0,1,0,0,...],  # 配体内部边
            [0,0,1,0,0,0,1,...],  # 蛋白质-配体边
        ], dtype=torch.float),

        name="compound_001"
    )
]
```

#### 处理后的四种图表示
```python
# 1. 原始复合物图
original_graph = Data(
    x=all_atoms_features,      # 蛋白质+配体原子
    edge_index=all_edges,      # 所有类型的边
    edge_attr=all_edge_features
)

# 2. 蛋白质图
protein_graph = Data(
    x=protein_atoms_features,   # 仅蛋白质原子
    edge_index=protein_edges,   # 仅蛋白质内部边
    edge_attr=protein_edge_features
)

# 3. 配体图
ligand_graph = Data(
    x=ligand_atoms_features,    # 仅配体原子
    edge_index=ligand_edges,    # 仅配体内部边（重新索引）
    edge_attr=ligand_edge_features
)

# 4. 蛋白质-配体交互图
interaction_graph = Data(
    x=all_atoms_features,       # 蛋白质+配体原子
    edge_index=pl_edges,        # 仅蛋白质-配体交互边
    edge_attr=pl_edge_features
)
```

## 9. 实际使用指南

### 9.1 环境准备

#### 依赖包安装
```bash
pip install torch torch-geometric
pip install rdkit-pypi
pip install scikit-learn
pip install numpy pandas
```

#### 硬件要求
- **最低配置**：8GB RAM, CPU
- **推荐配置**：16GB RAM, NVIDIA GPU (8GB+ VRAM)
- **高性能配置**：32GB RAM, NVIDIA GPU (16GB+ VRAM)

### 9.2 数据准备

#### 目录结构创建
```bash
mkdir -p /data/DUD_E/aa2ar/glide_graph_one
mkdir -p /data/DUD_E/ace/glide_graph_one
mkdir -p /data/DUD_E/ache/glide_graph_one
```

#### 数据文件命名规范
- 文件格式：`{compound_id}.pkl.gz`
- 示例：`CHEMBL123456.pkl.gz`, `ZINC000001.pkl.gz`
- 避免文件名中包含特殊字符

### 9.3 运行示例

#### 基本运行命令
```bash
python test_akscore2_v8_dude_screen_pred.py \
    --screen_dir /data/DUD_E \
    --target_name aa2ar \
    --model_path /models/akscore2_v8_best.pth \
    --result_save_dir /results/aa2ar_screening
```

#### 高性能配置运行
```bash
python test_akscore2_v8_dude_screen_pred.py \
    --screen_dir /data/DUD_E \
    --target_name aa2ar \
    --model_path /models/akscore2_v8_best.pth \
    --result_save_dir /results/aa2ar_screening \
    --batch_size 8 \
    --num_workers 8 \
    --hidden_dim 512 \
    --cut_edge_dis 8
```

#### 内存优化配置运行
```bash
python test_akscore2_v8_dude_screen_pred.py \
    --screen_dir /data/DUD_E \
    --target_name aa2ar \
    --model_path /models/akscore2_v8_best.pth \
    --result_save_dir /results/aa2ar_screening \
    --batch_size 2 \
    --num_workers 2 \
    --hidden_dim 128 \
    --cut_edge_dis 6
```

### 9.4 结果分析

#### 输出文件解读
```bash
# 查看预测结果
head -10 /results/aa2ar_screening/glide_sp_bind/aa2ar.txt

# 结果格式示例：
# CHEMBL123_001    0    -8.2341
# CHEMBL456_002    1    -7.8956
# CHEMBL789_003    2    -7.5432
```

#### 结果排序和筛选
```bash
# 按结合亲和力分数排序（越低越好）
sort -k3 -n /results/aa2ar_screening/glide_sp_bind/aa2ar.txt > aa2ar_sorted.txt

# 提取前100个最佳化合物
head -100 aa2ar_sorted.txt > aa2ar_top100.txt
```

### 9.5 常见问题和解决方案

#### 内存不足问题
```python
# 解决方案：减少batch_size和num_workers
--batch_size 1 --num_workers 1
```

#### GPU内存不足
```python
# 解决方案：使用CPU或减少模型参数
export CUDA_VISIBLE_DEVICES=""  # 强制使用CPU
--hidden_dim 64 --cut_edge_dis 4  # 减少模型复杂度
```

#### 数据文件损坏
```python
# 检查文件完整性
python -c "
import gzip, pickle
try:
    with gzip.open('compound.pkl.gz', 'rb') as f:
        data = pickle.load(f)
    print('File OK')
except:
    print('File corrupted')
"
```

这份技术文档提供了对 `test_akscore2_v8_dude_screen_pred.py` 脚本的全面分析，涵盖了从基本运行逻辑到具体数据结构的各个方面，为用户提供了深入理解和正确使用该脚本的完整指南。
