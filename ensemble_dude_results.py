import os
import numpy as np

result_dirs = [
        #### autodock gpu
        '/Arontier/People/junsuha/akscore_project/DUD_E',

    
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_300_tl0.054_tc0.997_vl0.035_vc0.972/dude_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.121_vl_6.902_vs1_0.682_vs5_0.848/dude_bind_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.477_vl_10.582_vs1_0.551_vs5_0.740/dude_bind',

#         '/Arontier/People/hongyiyu/Data/akscore2/dude/results_general/ens_v4_v8',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_300_tl0.054_tc0.997_vl0.035_vc0.972/dude_one_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.121_vl_6.902_vs1_0.682_vs5_0.848/dude_one_bind_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.477_vl_10.582_vs1_0.551_vs5_0.740/dude_one_bind',

    
    
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/122_v4/lr0.0001_bs32_nd72_ed12_hd256_nl5_do0.1/checkpoint_300_tl0.054_tc0.997_vl0.035_vc0.972/glide_sp_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v7/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.121_vl_6.902_vs1_0.682_vs5_0.848/glide_sp_bind_rmsd',
#     '/Arontier/People/hongyiyu/Data/akscore2/model_220829/1422_v8/lr0.0001_bs4_nd73_ed24_hd256_nl5_do0.1_ced8/checkpoint_300_tl_2.477_vl_10.582_vs1_0.551_vs5_0.740/glide_sp_bind',
    
    
        '/Arontier/People/hongyiyu/Data/akscore2/dude/results_general/ens_v4_v8_glidesp',
    
]

postfix = '.txt'
# postfix_ag = '_bind.txt'
# postfix_ag = '_bind_one.txt'
postfix_ag = '_bind_glide.txt'

combine_option = "plus"  ## multiply, plus
combine_coef_our = 0.4
combine_coef_ag = 1
# combine_option = "multiply" ## multiply, plus

save_dir = "/Arontier/People/hongyiyu/Data/akscore2/dude/results_general/ens_v4_v8_glidesp_ag_0.4_1"
# save_dir = "/Arontier/People/hongyiyu/Data/akscore2/dude/results_general/ens_v4_v8_glidesp"



if not os.path.exists(save_dir):
    os.makedirs(save_dir)

file_names = [f for f in os.listdir(result_dirs[1]) if f.endswith(postfix)]
for file_name in file_names:
    print(file_name)
    save_path = os.path.join(save_dir, file_name)

    result_dict_list = []
    for result_dir in result_dirs:
        print(result_dir)
        result_dict = {}

        result_path = os.path.join(result_dir, file_name)
        
        if not os.path.isfile(result_path):
            result_path = result_path.split(postfix)[0] + postfix_ag


        with open(result_path, 'r') as f:
            lines = f.readlines()

        for line in lines:
            line_splited = line.split()
            if line_splited[0].endswith('.pkl'):
                result_dict[line_splited[0][:-4]] = float(line_splited[2])
            else:
                result_dict[line_splited[0]] = float(line_splited[2])

        result_dict_list.append(result_dict)

    ### get keys
    result_keys = []
    for result_dict in result_dict_list:
        ## union
        result_keys = list(set(result_keys).union(set(result_dict.keys())))
        ## intersection
        # result_keys = list(set(result_keys).intersection(set(result_dict.keys())))

    ensemble_result_dict = {key: [] for key in result_keys}

    for dict in result_dict_list:
        for k, v in dict.items():
            ensemble_result_dict[k].append(v)

    write_lines = []
    for key_i, key in enumerate(ensemble_result_dict.keys()):
        item_values = ensemble_result_dict[key]

        if len(item_values) < 2:
            continue
        else:
            if combine_option == "multiply":
                ensemble_value = item_values[0] * item_values[1]
            else:
                ensemble_value = item_values[0] * combine_coef_ag + item_values[1] * combine_coef_our

        # ensemble_value = np.mean(item_values)
        write_lines.append(f"{key}\t{key_i}\t{ensemble_value:5f}")

    with open(save_path, 'w') as f:
        for line in write_lines:
            f.write("%s\n" % line)





